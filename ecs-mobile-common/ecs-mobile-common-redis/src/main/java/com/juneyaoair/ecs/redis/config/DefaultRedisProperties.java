package com.juneyaoair.ecs.redis.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description 默认的redis配置，此配置需要与auth鉴权服务保持一致保持一致
 * @date 2024/4/29 16:03
 */
@Component
@Data
public class DefaultRedisProperties {
    @Value("${spring.redis.password:}")
    private String password;
    @Value("${spring.redis.sentinel.master:}")
    private String master;
    @Value("${spring.redis.sentinel.cluster:}")
    private String cluster;
    @Value("${spring.redis.sentinel.database:0}")
    private int database;
}
