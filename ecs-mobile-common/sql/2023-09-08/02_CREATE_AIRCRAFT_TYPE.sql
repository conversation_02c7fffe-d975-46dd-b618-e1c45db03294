-- auto-generated definition
create table T_AIRCRAFT_TYPE
(
    AIRCRAFT_TYPE_ID   VARCHAR2(32) not null constraint T_AIRCRAFT_TYPE_PK primary key,
    AIRCRAFT_TYPE_CODE VARCHAR2(32) not null ,
    AIRCRAFT_TYPE_NAME VARCHAR2(64) not null ,
    BUSINESS_CLASS_NUM INTEGER,
    ECONOMY_CLASS_NUM  INTEGER,
    STATUS            VARCHAR2(1) default 'Y',
    CREATE_USER        VARCHAR2(32),
    CREATETIME         DATE,
    UPDATE_USER        VARCHAR2(32),
    UPDATETIME         DATE
);

comment on table T_AIRCRAFT_TYPE is '机型表';
comment on column T_AIRCRAFT_TYPE.AIRCRAFT_TYPE_ID is '主键ID';
comment on column T_AIRCRAFT_TYPE.AIRCRAFT_TYPE_CODE is '机型编码';
comment on column T_AIRCRAFT_TYPE.AIRCRAFT_TYPE_NAME is '机型名称';
comment on column T_AIRCRAFT_TYPE.BUSINESS_CLASS_NUM is '公务舱数量';
comment on column T_AIRCRAFT_TYPE.ECONOMY_CLASS_NUM is '经济舱数量';
comment on column T_AIRCRAFT_TYPE.STATUS is '状态 Y启用 N禁用';
comment on column T_AIRCRAFT_TYPE.CREATE_USER is '创建人';
comment on column T_AIRCRAFT_TYPE.CREATETIME is '创建时间';
comment on column T_AIRCRAFT_TYPE.UPDATE_USER is '更新人';
comment on column T_AIRCRAFT_TYPE.UPDATETIME is '更新时间';

create unique index AIRCRAFT_TYPE_CODE_UINDEX on T_AIRCRAFT_TYPE (AIRCRAFT_TYPE_CODE);
