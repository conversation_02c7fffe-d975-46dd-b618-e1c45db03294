<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="3 seconds">
	<property name="LOG_PATH" value="/opt/logs/"/>
	<property name="appName" value="ecs-mobile-admin"/>
	<property name="env" value="uat"/>

	<!-- 输出到控制台 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-6p --- [%15.15t][%X{requestId}] %-40.40logger{39} : %msg%n</Pattern>
		</encoder>
	</appender>

	<!-- 业务日志 -->
	<appender name="bizinfo" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_PATH}${appName}/bizlog/bizinfo.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}${appName}/bizlog/bizinfo-%d{yyyyMMdd}-%i.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>30MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>10</maxHistory>
			<totalSizeCap>5GB</totalSizeCap>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-6p --- [%15.15t] [${appName}][%X{requestId}] %-40.40logger{39}:%msg%n</Pattern>
		</layout>
	</appender>
	<!-- 访问详细日志 -->
	<appender name="accDetail" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_PATH}${appName}/accDetail/accdetail.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}${appName}/accDetail/accdetail-%d{yyyyMMdd}-%i.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>30MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>10</maxHistory>
			<totalSizeCap>5GB</totalSizeCap>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%msg%n</Pattern>
		</layout>
	</appender>
	<!-- 需要上传elk日志 -->
	<appender name="logELK" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_PATH}${appName}/metriclog/metric.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}${appName}/metriclog/metric-%d{yyyyMMdd}-%i.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>30MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>10</maxHistory>
			<totalSizeCap>5GB</totalSizeCap>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%msg%n</Pattern>
		</layout>
	</appender>

	<logger name="org.apache.catalina.startup.DigesterFactory" level="ERROR"/>
	<logger name="org.apache.catalina.util.LifecycleBase" level="ERROR"/>
	<logger name="org.apache.coyote.http11.Http11NioProtocol" level="ERROR"/>
	<logger name="org.apache.sshd.common.util.SecurityUtils" level="ERROR"/>
	<logger name="org.apache.tomcat.util.net.NioSelectorPool" level="ERROR"/>
	<logger name="org.eclipse.jetty.util.component.AbstractLifeCycle" level="ERROR"/>
	<logger name="org.hibernate.validator.internal.util.Version" level="ERROR"/>
	<logger name="org.springframework.kafka" level="ERROR"/>
	<logger name="org.apache.kafka" level="ERROR"/>
	<logger name="org.mongodb" level="ERROR"/>

	<logger name="com.elk.aspect" level="info" additivity="false">
		<appender-ref ref="logELK"/>
	</logger>
	<logger name="com.accDetail.aspect" level="info" additivity="false">
		<appender-ref ref="accDetail"/>
	</logger>
	<root level="debug">
		<appender-ref ref="console"/>
	</root>
	<root level="info">
		<appender-ref ref="bizinfo"/>
	</root>
</configuration>