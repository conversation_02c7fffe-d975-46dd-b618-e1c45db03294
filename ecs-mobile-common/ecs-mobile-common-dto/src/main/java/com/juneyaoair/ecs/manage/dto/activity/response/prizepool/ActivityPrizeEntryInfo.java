package com.juneyaoair.ecs.manage.dto.activity.response.prizepool;

import com.juneyaoair.ecs.manage.dto.activity.request.prizepool.PrizeSubEntityInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 奖品信息
 * @created 2023/11/29 13:35
 */
@Data
public class ActivityPrizeEntryInfo {

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "奖品编码")
    private String prizeCode;

    @ApiModelProperty(value = "奖品总数量")
    private Integer totalAmount;

    @ApiModelProperty(value = "奖品已发放数量")
    private Integer sendAmount;

    @ApiModelProperty(value = "奖品图片URL")
    private String iconUrl;

    @ApiModelProperty(value = "状态 Y:有效 N:无效 D:已删除")
    private String status;

    @ApiModelProperty(value = "子奖品信息清单")
    private List<PrizeSubEntityInfo> prizeSubEntityList;

}
