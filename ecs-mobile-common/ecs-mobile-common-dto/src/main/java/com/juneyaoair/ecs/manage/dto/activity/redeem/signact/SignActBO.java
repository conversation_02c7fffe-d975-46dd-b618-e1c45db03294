package com.juneyaoair.ecs.manage.dto.activity.redeem.signact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignActBO {

    @ApiModelProperty(value = "id", allowableValues = "", notes = "", example = "")
    private String id;

    @ApiModelProperty(value = "开始时间", allowableValues = "", notes = "", example = "")
    private Date actStartTime;

    @ApiModelProperty(value = "结束时间", allowableValues = "", notes = "", example = "")
    private Date actEndTime;

    @ApiModelProperty(value = "创建人", allowableValues = "", notes = "", example = "")
    private String createBy;

    @ApiModelProperty(value = "修改人", allowableValues = "", notes = "", example = "")
    private String updateBy;

    @ApiModelProperty(value = "审核人", allowableValues = "", notes = "", example = "")
    private String auditedBy;

    @ApiModelProperty(value = "是否已审核", allowableValues = "", notes = "", example = "")
    private boolean audited;

    @ApiModelProperty(value = "奖品", allowableValues = "", notes = "", example = "")
    private List<SignAwardBO> awardList;
}
