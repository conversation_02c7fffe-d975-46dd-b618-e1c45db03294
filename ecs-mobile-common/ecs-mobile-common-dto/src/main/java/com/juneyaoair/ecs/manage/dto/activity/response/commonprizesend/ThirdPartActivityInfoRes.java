package com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend;

import lombok.Builder;

/**
 * @ClassName IndefiniteQuantityActivityInfo
 * @Description
 * <AUTHOR>
 * @Date 2025/4/1 12:42
 * @Version 1.0
 */
@Builder
public class ThirdPartActivityInfoRes {

    /**
     * <AUTHOR>
     * @Description 活动号
     * @Date 12:43 2025/4/1
     **/
    private String activityCode;

    /**
     * <AUTHOR>
     * @Description 活动名称
     * @Date 12:43 2025/4/1
     **/
    private String activityName;

    public ThirdPartActivityInfoRes() {
    }

    public ThirdPartActivityInfoRes(String activityCode, String activityName) {
        this.activityCode = activityCode;
        this.activityName = activityName;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
}
