package com.juneyaoair.ecs.manage.dto.flightdistance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlightDistancePricedDTO {
    private String id;
    private String depCity;
    private String arrCity;
    private String mileage;
    private String priceY;
    private String priceJ;
    private String createdUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createdTime;
    private String updatedUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updatedTime;
    //状态
    @Pattern(regexp = "[YN]", message = "The status value should be either 'Y' or 'N'")
    private String status;

}
