package com.juneyaoair.ecs.manage.dto.apppictureconsole;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 首屏展示
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LoadPic {
    public String picId;
    public String startTime;
    public String endTime;
    public String picUrl;
    public String title;//名称
    public String url;
    public String winName;//窗口名称
    public String description;
    public String isLogin;//判断是否需要登录
    public long displayTime;//首屏展示时长  5000ms
}
