package com.juneyaoair.ecs.manage.dto.activity.request.event;

import com.juneyaoair.ecs.manage.dto.base.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 查询事件奖品发放清单
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EventMemberQuery extends PageBase {

    @NotBlank(message = "事件类型不能为空")
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "会员卡号")
    private String ffpCardNo;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

}
