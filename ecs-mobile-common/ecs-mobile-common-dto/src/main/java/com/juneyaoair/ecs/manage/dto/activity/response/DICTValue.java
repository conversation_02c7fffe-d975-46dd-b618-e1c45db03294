package com.juneyaoair.ecs.manage.dto.activity.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 数据字典数据项实体类对应数据库表TFB_DICTVALUE
 * @Date 16:11 2024/1/23
 **/
public class DICTValue implements Serializable{

	private static final long serialVersionUID = 553982153837614513L;



	
	private String dvid;//主键
	private String dvCode;//编码
	private String dvName;//名称
	private String dvDescription;//描述
	private String enable;//是否有效
	private String dtid;//对应的类型id
	// 查询条件   返回结果中也要dtcode  modify by jason 2014-09-22
	private String dtCode;
	//用于后台模块管理中父模块排序
	private String num;

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public String getNum() {
		return num;
	}

	public void setNum(String num) {
		this.num = num;
	}

	public DICTValue() {
		super();
	}
	public DICTValue(String dvid, String dvCode, String dvName,
                     String dvDescription, String enable, String dtid) {
		super();
		this.dvid = dvid;
		this.dvCode = dvCode;
		this.dvName = dvName;
		this.dvDescription = dvDescription;
		this.enable = enable;
		this.dtid = dtid;
	}
	public String getDvid() {
		return dvid;
	}
	public void setDvid(String dvid) {
		this.dvid = dvid;
	}
	public String getDvCode() {
		return dvCode;
	}
	public void setDvCode(String dvCode) {
		this.dvCode = dvCode;
	}
	public String getDvName() {
		return dvName;
	}
	public void setDvName(String dvName) {
		this.dvName = dvName;
	}

	public String getDvDescription() {
		return dvDescription;
	}
	public void setDvDescription(String dvDescription) {
		this.dvDescription = dvDescription;
	}
	public String getEnable() {
		return enable;
	}
	public void setEnable(String enable) {
		this.enable = enable;
	}
	public String getDtid() {
		return dtid;
	}
	public void setDtid(String dtid) {
		this.dtid = dtid;
	}
	/**
	 * @return dtCode
	 */
	
	public String getDtCode() {
		return dtCode;
	}
	/**
	 * @param dtCode dtCode
	 */
	
	public void setDtCode(String dtCode) {
		this.dtCode = dtCode;
	}
	
	
}
