package com.juneyaoair.ecs.manage.dto.avcabin.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/8 10:21
 */
@Data
@ApiModel(value = "AvCabinParam",description = "航班舱位请求DTO")
public class AvCabinParam {
    @ApiModelProperty("开始时间")
    @NotBlank(message = "开始时间不可为空")
    private String startTime;


    @ApiModelProperty("结束时间")
    @NotBlank(message = "结束时间不可为空")
    private String endTime;


    @ApiModelProperty("出发城市三字码")
    @NotBlank(message = "出发城市三字码不可为空")
    private String depCity;


    @ApiModelProperty("到达城市三字码")
    @NotBlank(message = "到达城市三字码不可为空")
    private String arrCity;
}
