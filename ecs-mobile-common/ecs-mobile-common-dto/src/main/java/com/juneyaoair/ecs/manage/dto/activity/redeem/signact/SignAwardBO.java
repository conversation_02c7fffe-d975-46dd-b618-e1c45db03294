package com.juneyaoair.ecs.manage.dto.activity.redeem.signact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignAwardBO {

    @ApiModelProperty(value = "id", allowableValues = "", notes = "", example = "")
    private String id;

    @ApiModelProperty(value = "奖品名称", allowableValues = "", notes = "", example = "")
    private String awardName;

    @ApiModelProperty(value = "奖品描述", allowableValues = "", notes = "", example = "")
    private String awardDesc;

    @ApiModelProperty(value = "每月限制", allowableValues = "", notes = "", example = "")
    private String monthlyLimit;

    @ApiModelProperty(value = "所需积分", allowableValues = "", notes = "", example = "")
    private String needPoint;

    @ApiModelProperty(value = "奖池编码", allowableValues = "", notes = "", example = "")
    private String prizePoolCode;

}
