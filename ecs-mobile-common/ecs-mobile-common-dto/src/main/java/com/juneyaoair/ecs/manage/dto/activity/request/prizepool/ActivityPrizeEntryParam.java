package com.juneyaoair.ecs.manage.dto.activity.request.prizepool;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: caolei
 * @Description: 创建活动奖品信息表
 * @Date: 2024/07/09 9:15
 * @Modified by:
 */
@Data
public class ActivityPrizeEntryParam {

    @ApiModelProperty(value = "奖池编码")
    private String prizePoolCode;

    @ApiModelProperty(value = "奖品编码")
    private String prizeCode;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "奖品总数")
    private Integer totalAmount;

    @ApiModelProperty(value = "奖品图片URL")
    private String iconUrl;

    @ApiModelProperty(value = "状态 D：删除")
    private String status;

    @ApiModelProperty(value = "子奖品信息清单")
    private List<PrizeSubEntityInfo> prizeSubEntityList;

    @ApiModelProperty(value = "操作人", hidden = true)
    private String operator;

}
