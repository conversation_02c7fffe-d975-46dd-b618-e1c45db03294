package com.juneyaoair.ecs.manage.dto.travelreminder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TravelReminderListDTO {
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "提醒主题")
    private String theme;
    
    @ApiModelProperty(value = "渠道")
    private String channel;
    
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
    
    @ApiModelProperty(value = "生效时间")
    private Date startTime;
    
    @ApiModelProperty(value = "失效时间")
    private Date endTime;
    
    @ApiModelProperty(value = "维护人")
    private String maintainer;
    
    @ApiModelProperty(value = "操作类型(状态）", example = "SAVE-保存，SUBMIT-提交（发布）")
    private String operationType;

    @ApiModelProperty(value = "适用航线类型", example = "A-全部、D-国内、I-国际")
    private String routeType;
    
    @ApiModelProperty(value = "可用航线对", example = "*代表全部,Domestic代表国内,INTL代表国际及港澳台,多城市对用英文逗号\",\"隔开")
    private String availableRoute;
    
    @ApiModelProperty(value = "不可用航线对", example = "*代表全部,Domestic代表国内,INTL代表国际及港澳台,多城市对用英文逗号\",\"隔开")
    private String unavailableRoute;
    
    @ApiModelProperty(value = "顺序", example = "1、2、3")
    private Integer sort;

    @ApiModelProperty(value = "文案内容列表")
    private List<TravelReminderContentDTO> contents;
} 