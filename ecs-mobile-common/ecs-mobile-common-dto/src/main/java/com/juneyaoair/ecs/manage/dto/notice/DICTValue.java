package com.juneyaoair.ecs.manage.dto.notice;


/**
 * <AUTHOR>
 * @Description 数据字典数据项实体类对应数据库表TFB_DICTVALUE
 * @Date 10:07 2023/12/21
 **/
public class DICTValue {
    /**
     * <AUTHOR>
     * @Description 主键
     * @Date 10:08 2023/12/21
     **/
    private String dvid;

    /**
     * <AUTHOR>
     * @Description 编码
     * @Date 10:08 2023/12/21
     **/
    private String dvCode;

    /**
     * <AUTHOR>
     * @Description 名称
     * @Date 10:08 2023/12/21
     **/
    private String dvName;

    /**
     * <AUTHOR>
     * @Description 描述
     * @Date 10:08 2023/12/21
     **/
    private String dvDescription;

    /**
     * <AUTHOR>
     * @Description 是否有效
     * @Date 10:08 2023/12/21
     **/
    private String enable;

    /**
     * <AUTHOR>
     * @Description 对应的类型id
     * @Date 10:08 2023/12/21
     **/
    private String dtid;
    private String dtCode;

    //用于后台模块管理中父模块排序
    private String num;

    public DICTValue() {
    }

    public DICTValue(String dvid, String dvCode, String dvName, String dvDescription, String enable, String dtid, String dtCode, String num) {
        this.dvid = dvid;
        this.dvCode = dvCode;
        this.dvName = dvName;
        this.dvDescription = dvDescription;
        this.enable = enable;
        this.dtid = dtid;
        this.dtCode = dtCode;
        this.num = num;
    }

    public String getDvid() {
        return dvid;
    }

    public void setDvid(String dvid) {
        this.dvid = dvid;
    }

    public String getDvCode() {
        return dvCode;
    }

    public void setDvCode(String dvCode) {
        this.dvCode = dvCode;
    }

    public String getDvName() {
        return dvName;
    }

    public void setDvName(String dvName) {
        this.dvName = dvName;
    }

    public String getDvDescription() {
        return dvDescription;
    }

    public void setDvDescription(String dvDescription) {
        this.dvDescription = dvDescription;
    }

    public String getEnable() {
        return enable;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public String getDtid() {
        return dtid;
    }

    public void setDtid(String dtid) {
        this.dtid = dtid;
    }

    public String getDtCode() {
        return dtCode;
    }

    public void setDtCode(String dtCode) {
        this.dtCode = dtCode;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }
}
