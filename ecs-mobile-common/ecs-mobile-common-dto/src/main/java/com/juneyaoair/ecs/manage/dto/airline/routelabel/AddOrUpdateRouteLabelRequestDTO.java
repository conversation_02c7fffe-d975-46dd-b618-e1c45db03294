package com.juneyaoair.ecs.manage.dto.airline.routelabel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddOrUpdateRouteLabelRequestDTO {

    @ApiModelProperty(value = "主键", example = "uuid")
    private String id;


    @ApiModelProperty(value = "标签名称", example = "中转住宿")
    @NotBlank(message = "标签名称不能为空")
    private String labelName;
    /**
     * 标签图片
     */
    @ApiModelProperty(value = "标签图片")
    private String labelImg;
    /**
     * 展示区域标题
     */
    @ApiModelProperty(value = "展示区域标题")
    private String displayAreaTitle;
    /**
     * 展示区域副文字
     */
    @ApiModelProperty(value = "展示区域副文字")
    private String displayViceText;
    /**
     * 展示区域主文
     */
    @ApiModelProperty(value = "展示区域主文")
    private String displayMainText;

    @ApiModelProperty(value = "生效时间", example = "yyyy-MM-dd HH:mm:ss")
    @NotBlank(message = "生效时间不能为空")
    private String startDate;


    @ApiModelProperty(value = "失效时间", example = "yyyy-MM-dd HH:mm:ss")
    @NotBlank(message = "失效时间不能为空")
    private String endDate;


    @ApiModelProperty(value = "标签功能;中转住宿:TransitAccommodation", example = "TransitAccommodation")
    private String labelFunction;


    @ApiModelProperty(value = "标签展示类型", example = "普通标签/活动标签/主要标签,normal/activity/main")
    private String labelDisplayType;


    @ApiModelProperty(value = "启用状态;启用/禁用", example = "true")
    private boolean enableStatus;


    @ApiModelProperty(value = "排序", example = "1")
    @NotNull(message = "排序号不能为空")
    private Integer sortNum;

    @ApiModelProperty(value = "渠道列表")
    @NotEmpty(message = "渠道不能为空")
    private List<RouteLabelChannelDTO> channelList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RouteLabelChannelDTO {

        @ApiModelProperty(value = "主键", example = "uuid")
        private String channelId;


        @ApiModelProperty(value = "渠道", example = "MOBILE")
        private String channel;


        @ApiModelProperty(value = "渠道链接", example = "url")
        private String channelUrl;

    }

    @AssertTrue(message = "标签功能格式不正确")
    public boolean isLabelFunctionEnable() {
        if (Strings.isNotBlank(labelFunction)) {
            // 现仅有 TransitAccommodation
            if (!"TransitAccommodation".equals(labelFunction)) {
                return false;
            }
        }
        return true;
    }

    @AssertTrue(message = "渠道不能为空")
    public boolean isChannelEnable() {
        return channelList.isEmpty() || channelList.stream().noneMatch(v -> StringUtils.isEmpty(v.getChannel()));
    }
}
