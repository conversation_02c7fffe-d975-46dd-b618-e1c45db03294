package com.juneyaoair.ecs.manage.dto.activity.request.coupon;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName ThirdCouponeRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/4/1 21:24
 * @Version 1.0
 */
public class ThirdPartPrizeRequest {
    /**
     * <AUTHOR>
     * @Description 活动号
     * @Date 21:29 2025/4/1
     **/
    @NotEmpty(message = "活动号不可为空")
    private String activityCode;

    /**
     * <AUTHOR>
     * @Description 商户号
     * @Date 21:29 2025/4/1
     **/
    private String merchantCode;

    /**
     * <AUTHOR>
     * @Description 奖品编码
     * @Date 21:29 2025/4/1
     **/
    private String prizeCode;

    /**
     * <AUTHOR>
     * @Description 奖品名称
     * @Date 21:29 2025/4/1
     **/
    private String prizeName;

    /**
     * <AUTHOR>
     * @Description 奖品类型
     * @Date 19:43 2025/4/2
     **/
    private String prizeType;


    /**
     * <AUTHOR>
     * @Description 奖品状态
     * @Date 21:29 2025/4/1
     **/
    private String prizeStatus;

    /**
     * <AUTHOR>
     * @Description 使用卡号
     * @Date 21:29 2025/4/1
     **/
    private String ffpCardNo;


    public ThirdPartPrizeRequest() {
    }

    public ThirdPartPrizeRequest(String activityCode, String merchantCode, String prizeCode, String prizeName, String prizeType, String prizeStatus, String ffpCardNo) {
        this.activityCode = activityCode;
        this.merchantCode = merchantCode;
        this.prizeCode = prizeCode;
        this.prizeName = prizeName;
        this.prizeType = prizeType;
        this.prizeStatus = prizeStatus;
        this.ffpCardNo = ffpCardNo;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getPrizeCode() {
        return prizeCode;
    }

    public void setPrizeCode(String prizeCode) {
        this.prizeCode = prizeCode;
    }

    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    public String getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(String prizeType) {
        this.prizeType = prizeType;
    }

    public String getPrizeStatus() {
        return prizeStatus;
    }

    public void setPrizeStatus(String prizeStatus) {
        this.prizeStatus = prizeStatus;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

}
