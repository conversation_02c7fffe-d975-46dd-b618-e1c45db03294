package com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName IntegratingMarketingTotalQueryRequest
 * @Description 全域营销汇总信息查询
 * <AUTHOR>
 * @Date 2025/6/29 10:53
 * @Version 1.0
 */
@Data
public class IntegratingMarketingTotalQueryRequest {

    /**
     * <AUTHOR>
     * @Description 域账号
     * @Date 10:56 2025/6/29
     **/
    private String domainAccount;

    /**
     * <AUTHOR>
     * @Description 关联开始时间
     * @Date 10:56 2025/6/29
     **/
    @NotEmpty(message = "关联开始时间不可为空")
    private String associatedStartTime;

    /**
     * <AUTHOR>
     * @Description 关联结束时间
     * @Date 10:56 2025/6/29
     **/
    @NotEmpty(message = "关联结束时间不可为空")
    private String associatedEndTime;

    /**
     * <AUTHOR>
     * @Description 员工会员卡号
     * @Date 10:56 2025/6/29
     **/
    private String employeeCardNo;

    /**
     * <AUTHOR>
     * @Description 员工所属部门
     * @Date 10:56 2025/6/29
     **/
    private String department;

    /**
     * <AUTHOR>
     * @Description 出行开始时间
     * @Date 10:56 2025/6/29
     **/
    @NotEmpty(message = "出行开始时间不可为空")
    private String tripStartTime;

    /**
     * <AUTHOR>
     * @Description 出行结束时间
     * @Date 10:56 2025/6/29
     **/
    @NotEmpty(message = "出行结束时间不可为空")
    private String tripEndTime;

    private Integer pageNum;     // 当前页码

    private Integer pageSize;    // 每页数量


    /**
     * 用于筛选 C 表中的数据：ASSOCIATED_TIME >= 此时间
     */
    private String countStartTime;

    /**
     * 用于筛选 C 表中的数据：ASSOCIATED_TIME <= 此时间
     */
    private String countEndTime;
}
