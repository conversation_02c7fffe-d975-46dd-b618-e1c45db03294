package com.juneyaoair.ecs.manage.dto.activity.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActivityCoupon {
    private String id;
    @NotNull(message="活动名称不能为空")
    private String couponGiftName;
    @NotNull(message="开始时间不能为空")
    private String activityStartTime;
    @NotNull(message="结束时间不能为空")
    private String activityEndTime;
    @NotNull(message = "跳转链接不能为空")
    private String redirectUrl;

    @NotNull(message = "活动规则不能为空")
    private String activityRules;
    @NotNull(message = "背景图不能为空")
    private String couponGiftImageUrl;
    @NotNull(message = "背景色不能为空")
    private String bgColor;
    @NotNull(message = "活动类型不能为空")
    private String couponGiftType;

    private String activityUrl;

    @NotNull(message = "页面模板不能为空")
    private String modelUrl;
    @NotNull(message = "礼包名称不能为空")
    private String packageName;
    /** 领取方式(1:登录领取；2.手机号领取；3：支付购买 4：权益兑换) */
    @NotNull(message = "领取方式不能为空")
    private String getStyle;
    @NotNull(message = "领取限制不能为空")
    private String getLimit;

    private String canShare;
    private String shareTitle;
    private String shareDesc;
    private String shareLink;
    private String shareImgUrl;

    private String getLevel;
    private String memberYears;
    private String memberCardOrPhoneNum;
    private String memberCardOrPhoneNumDesc;

    private String createUser;
    private String createDate;
    private String lastUpdateUser;
    private String lastUpdateDate;
    private String remark;
    private String isValid;

    private String packageNum;
    private String packagePrice;
    private String upperLimit;
    private String packageNameColor;
    private String vCodeCheck;
    private String vCode;
    private String showType;
    private Integer stock;
    private Integer changeStock;
    private String nameAuthentication;
    /**
     * 收款商户 JX:吉祥  JN:吉宁
     */
    private String merchantPayment;
}
