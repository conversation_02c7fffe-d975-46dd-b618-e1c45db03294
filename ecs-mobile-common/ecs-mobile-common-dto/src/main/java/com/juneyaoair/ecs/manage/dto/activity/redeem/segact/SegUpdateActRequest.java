package com.juneyaoair.ecs.manage.dto.activity.redeem.segact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class SegUpdateActRequest {

    @ApiModelProperty(value = "id", allowableValues = "", notes = "", example = "")
    private String id;

    @ApiModelProperty(value = "期次", allowableValues = "", notes = "", example = "")
    @NotNull
    private Date actStartTime;

    @ApiModelProperty(value = "期次", allowableValues = "", notes = "", example = "")
    @NotNull
    private Date actEndTime;

    @ApiModelProperty(value = "奖品列表", allowableValues = "", notes = "", example = "")
    private List<SegActAwardDTO> awardList;

}
