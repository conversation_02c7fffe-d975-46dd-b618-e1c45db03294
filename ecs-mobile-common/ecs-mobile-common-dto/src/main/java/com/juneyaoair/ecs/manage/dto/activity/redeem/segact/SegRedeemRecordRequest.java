package com.juneyaoair.ecs.manage.dto.activity.redeem.segact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SegRedeemRecordRequest {

    @ApiModelProperty(value = "期次开始", allowableValues = "", notes = "", example = "")
    private String actStartTime;

    @ApiModelProperty(value = "期次结束", allowableValues = "", notes = "", example = "")
    private String actEndTime;

    @ApiModelProperty(value = "会员卡号", allowableValues = "", notes = "", example = "")
    private String ffpNo;

}
