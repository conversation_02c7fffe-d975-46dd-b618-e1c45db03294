package com.juneyaoair.ecs.manage.dto.picture;

import com.juneyaoair.ecs.manage.dto.citymanage.FileInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PictureConsoleSearchReq {
    @ApiModelProperty("目录id")
    private String catalogId;

    @ApiModelProperty(hidden = false)
    private String picId;
    @ApiModelProperty(hidden = false)
    private String picStarttime;
    @ApiModelProperty(hidden = false)
    private String picEndtime;
    @ApiModelProperty(hidden = false)
    private String picLocation;
    @ApiModelProperty(hidden = false)
    private String picUrl;
    @ApiModelProperty(hidden = false)
    private String url;
    @ApiModelProperty(hidden = false)
    private String title;
    @ApiModelProperty(hidden = false)
    private String description;
    @ApiModelProperty(hidden = false)
    private String descriptionPlainTxt;
    @ApiModelProperty(hidden = false)
    private String status;
    @ApiModelProperty(hidden = false)
    private String statusCode;
    @ApiModelProperty(hidden = false)
    private String updateMan;
    @ApiModelProperty(hidden = false)
    private String updateTime;
    @ApiModelProperty(hidden = false)
    private String createTime;
    @ApiModelProperty(hidden = false)
    private String createMan;

    @ApiModelProperty(hidden = false)
    private List<String> channel;
    @ApiModelProperty(hidden = false)
    private String channelCode;
    //保存時文件的ID
    @ApiModelProperty("保存時文件的ID")
    private String[] fildIds;//之前是fildIds
    @ApiModelProperty(hidden = false)
    private List<FileInfoDTO> fileInfos;
    @ApiModelProperty("判断是否需要登录")
    private String isLogin;//判断是否需要登录
    @ApiModelProperty("图片发布模式")
    private String modeType; //图片发布模式

    @ApiModelProperty("是否可分享")
    private String isShared; //是否可分享
    @ApiModelProperty("缩略图")
    private String shareIconUrl;//缩略图
    @ApiModelProperty("分享描述")
    private String shareDesc;//分享描述
    @ApiModelProperty("是否送积分")
    private String isGiftPoints;//是否送积分
    @ApiModelProperty("是否送优惠券")
    private String isGiftCoupons;//是否送优惠券

    @ApiModelProperty("支持的最小版本")
    private String minVer;  //支持的最小版本
    @ApiModelProperty("支持的最大版本")
    private String maxVer;  //支持的最大版本
    @ApiModelProperty("平台信息")
    private String platformInfo;//平台信息
    @ApiModelProperty("语言")
    private String language;
}
