package com.juneyaoair.ecs.manage.dto.activity.redeem.signact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SignUpdateActRequest {

    @ApiModelProperty(value = "id", allowableValues = "", notes = "", example = "")
    private String id;

    @ApiModelProperty(value = "期次", allowableValues = "", notes = "", example = "")
    private Date actStartTime;

    @ApiModelProperty(value = "期次", allowableValues = "", notes = "", example = "")
    private Date actEndTime;

    @ApiModelProperty(value = "奖品列表", allowableValues = "", notes = "", example = "")
    private List<SignActAwardDTO> awardList;

}
