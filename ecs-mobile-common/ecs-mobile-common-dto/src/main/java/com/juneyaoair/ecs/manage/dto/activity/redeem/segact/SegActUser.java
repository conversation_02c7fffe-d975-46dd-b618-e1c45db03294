package com.juneyaoair.ecs.manage.dto.activity.redeem.segact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SegActUser {

    @ApiModelProperty(value = "会员卡号", allowableValues = "", notes = "", example = "")
    private String ffpNo;

    @ApiModelProperty(value = "飞行券", allowableValues = "", notes = "", example = "")
    private String pointCount;

    @ApiModelProperty(value = "飞行券下月过期", allowableValues = "", notes = "", example = "")
    private String pointNextExpiredCount;

    @ApiModelProperty(value = "飞行券pro", allowableValues = "", notes = "", example = "")
    private String proPointCount;

    @ApiModelProperty(value = "飞行券pro下月过期", allowableValues = "", notes = "", example = "")
    private String proPointNextExpiredCount;
}
