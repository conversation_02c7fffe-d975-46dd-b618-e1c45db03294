package com.juneyaoair.ecs.manage.dto.activity.redeem.segact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SegAddActRequest {


    @ApiModelProperty(value = "期次", allowableValues = "", notes = "", example = "")
    private Date actStartTime;

    @ApiModelProperty(value = "期次", allowableValues = "", notes = "", example = "")
    private Date actEndTime;

    @ApiModelProperty(value = "奖品列表", allowableValues = "", notes = "", example = "")
    private List<SegActAwardDTO> awardList;

}
