package com.juneyaoair.ecs.manage.dto.discount.ro;


import com.juneyaoair.ecs.manage.dto.activity.common.ActivityAirLine;
import lombok.Data;

import java.util.List;

@Data
public class ThreeTitleDataRo {


    private String id;

    /**
     * 父级标题id
     */
    private String parentTitleId;

    /**
     * 三级标题
     */
    private String  name;

    /**
     * 数据组名称
     */
    private String   dataSetName;


    /**
     * 航线数据
     */
    List<ActivityAirLine>  airLinDataSet;

}
