package com.juneyaoair.ecs.manage.dto.version.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName VersionControlResponse
 * @Description
 * <AUTHOR>
 * @Date 2024/3/15 9:21
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VersionControlResponse {

    private Integer versionID;

    private String channelCode;

    private String versionType;

    private String versionNo;

    private String ios;

    private String android;

    private String iosVersion;

    private String andVersion;

    private String updateDetails;
}
