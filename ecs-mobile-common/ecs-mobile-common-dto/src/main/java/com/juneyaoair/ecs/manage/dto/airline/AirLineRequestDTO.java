package com.juneyaoair.ecs.manage.dto.airline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class AirLineRequestDTO{
    @ApiModelProperty("出发城市")
    private String depCity;
    @ApiModelProperty("到达城市")
    private String arrCity;
    @ApiModelProperty("航线标识")
    private String addonRemark;
    @ApiModelProperty("出发机场")
    private String depAirport;
    @ApiModelProperty("到达机场")
    private String arrAirport;
    /**
     * Y:经停 N：直达 M：联程航线国内 F：联程航线国外
     */
    @ApiModelProperty("Y:经停 N：直达 M：联程航线国内 F：联程航线国外")
    private String isTransit;

    @ApiModelProperty("是吉祥航线")
    private String isHoLine;

    @ApiModelProperty("航线标签")
    private String airLineLabel;
}
