package com.juneyaoair.ecs.manage.dto.notice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MaintainInfo {
    /**
     * <AUTHOR>
     * @Description 主键
     * @Date 10:17 2023/12/21
     **/
    private String ntMaintainID;

    /**
     * <AUTHOR>
     * @Description 模块名
     * @Date 10:17 2023/12/21
     **/
    private String ntMaintainName;

    /**
     * <AUTHOR>
     * @Description
     * @Date 10:17 2023/12/21
     **/
    private String fileNames;

    /**
     * <AUTHOR>
     * @Description PDF等其他地址
     * @Date 10:17 2023/12/21
     **/
    private String ntOtherUrl;

    /**
     * <AUTHOR>
     * @Description 图片地址
     * @Date 10:17 2023/12/21
     **/
    private String ntPicUrl;

    /**
     * <AUTHOR>
     * @Description 创建时间
     * @Date 10:17 2023/12/21
     **/
    private String createTime;

    /**
     * <AUTHOR>
     * @Description 修改时间
     * @Date 10:17 2023/12/21
     **/
    private String modifyTime;

    /**
     * <AUTHOR>
     * @Description 创建人
     * @Date 10:17 2023/12/21
     **/
    private String person;

    /**
     * <AUTHOR>
     * @Description 模块编号
     * @Date 10:17 2023/12/21
     **/
    private String ntMaintainCode;

    /**
     * <AUTHOR>
     * @Description 模块描述信息
     * @Date 10:17 2023/12/21
     **/
    @ApiModelProperty(value = "条款备注描述")
    private String ntMaintainDesciption;

    /**
     * <AUTHOR>
     * @Description 富文本内容
     * @Date 10:17 2023/12/21
     **/
    private byte[] richTexts;

    //===================以下为数据库非持久字段==================
    //富文本前端请求参数
    private String richContext;
    //一级模块编码
    private String modularBm;
    //一级模块名称
    private String dtName;
    //二级模块编码
    private String noticeBm;
    //二级模块名称
    private String ntName;
}
