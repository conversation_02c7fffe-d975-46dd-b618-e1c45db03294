package com.juneyaoair.ecs.manage.dto.activity.request.passport;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName PassportRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/4/2 21:18
 * @Version 1.0
 */
public class PassportRequest {
    /**
     * <AUTHOR>
     * @Description 活动号
     * @Date 21:46 2025/4/2
     **/
    @NotEmpty(message = "活动号不能为空")
    private String activityCode;

    /**
     * <AUTHOR>
     * @Description 奖品领取开始时间
     * @Date 21:46 2025/4/2
     **/
    @NotEmpty(message = "奖品领取开始时间不能为空")
    private String receiveStartTime;

    /**
     * <AUTHOR>
     * @Description 奖品领取结束时间
     * @Date 13:07 2025/3/17
     **/
    @NotEmpty(message = "奖品领取结束时间不能为空")
    private String receiveEndTime;

    /**
     * <AUTHOR>
     * @Description 会员卡号
     * @Date 21:46 2025/4/2
     **/
    private String ffpCardNo;

    /**
     * <AUTHOR>
     * @Description 奖品ID
     * @Date 21:46 2025/4/2
     **/
    private String prizeId;

    /**
     * <AUTHOR>
     * @Description 领取状态
     * @Date 21:46 2025/4/2
     **/
    private String prizeStatus;

    public PassportRequest() {
    }

    public PassportRequest(String activityCode, String receiveStartTime, String receiveEndTime, String ffpCardNo, String prizeId, String prizeStatus) {
        this.activityCode = activityCode;
        this.receiveStartTime = receiveStartTime;
        this.receiveEndTime = receiveEndTime;
        this.ffpCardNo = ffpCardNo;
        this.prizeId = prizeId;
        this.prizeStatus = prizeStatus;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getReceiveStartTime() {
        return receiveStartTime;
    }

    public void setReceiveStartTime(String receiveStartTime) {
        this.receiveStartTime = receiveStartTime;
    }

    public String getReceiveEndTime() {
        return receiveEndTime;
    }

    public void setReceiveEndTime(String receiveEndTime) {
        this.receiveEndTime = receiveEndTime;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getPrizeId() {
        return prizeId;
    }

    public void setPrizeId(String prizeId) {
        this.prizeId = prizeId;
    }

    public String getPrizeStatus() {
        return prizeStatus;
    }

    public void setPrizeStatus(String prizeStatus) {
        this.prizeStatus = prizeStatus;
    }
}
