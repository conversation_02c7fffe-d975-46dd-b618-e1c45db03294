package com.juneyaoair.ecs.manage.dto.activity.redeem.signact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SignRedeemRecordRequest {

    @ApiModelProperty(value = "期次开始", allowableValues = "", notes = "", example = "")
    private String actStartTime;

    @ApiModelProperty(value = "期次结束", allowableValues = "", notes = "", example = "")
    private String actEndTime;

    @ApiModelProperty(value = "会员卡号", allowableValues = "", notes = "", example = "")
    private String ffpNo;


}
