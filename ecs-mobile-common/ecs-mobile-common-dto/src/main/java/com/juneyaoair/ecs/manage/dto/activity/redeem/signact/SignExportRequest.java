package com.juneyaoair.ecs.manage.dto.activity.redeem.signact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

@Data
public class SignExportRequest {

    @ApiModelProperty(value = "会员卡号", allowableValues = "", notes = "", example = "")
    @NotNull
    private String ffpNo;

    @ApiModelProperty(value = "月度", allowableValues = "", notes = "", example = "202310")
    private String month;
}
