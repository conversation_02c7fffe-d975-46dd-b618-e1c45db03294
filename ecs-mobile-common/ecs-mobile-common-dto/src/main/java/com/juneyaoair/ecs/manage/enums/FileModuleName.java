package com.juneyaoair.ecs.manage.enums;

/**
 * <AUTHOR>
 * @description 文件上传附件模块名
 * @date 2023/5/15 11:13
 */
public enum FileModuleName {
    MESSAGE("message","公告"),

    ADVERTISEMENT("advertisement","广告位"),
    CLAUSE("clause","协议条款");

    /**
     * 业务模块
     */
    private String moduleName;
    /**
     * 业务模块描述
     */
    private String desc;
    FileModuleName(String moduleName, String desc) {
        this.moduleName = moduleName;
        this.desc = desc;
    }

    public String getModuleName() {
        return moduleName;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean checkModule(String v){
        for (FileModuleName c: FileModuleName.values()) {
            if (c.moduleName.equals(v)) {
                return true;
            }
        }
        return false;
    }
}
