package com.juneyaoair.ecs.seat.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 值机选座-列表清单查询
 * @created 2024/03/14 9:43
 */
@Data
public class SeatBaseOpenStatus {

    @NotEmpty(message = "规则ID不能为空")
    @ApiModelProperty(value = "规则ID", required = true)
    private Set<Long> setIds;

    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "状态", required = true)
    private Boolean enable;

}
