package com.juneyaoair.ecs.manage.dto.activity.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2019-2-20 8:51
 * @description：城市标签
 * @modified By：
 * @version: $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CityLabelInfo {
    private Integer cityLabelId;
    private String cityLabelName;
    private String cityLabelUrl;
    private String cityCode;
    private Date updateTime;
    private String updateUser;
    private String createUser;
    private String labelIntroduce;

}


