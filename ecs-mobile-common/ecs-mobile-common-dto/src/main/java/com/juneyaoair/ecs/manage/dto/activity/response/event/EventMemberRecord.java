package com.juneyaoair.ecs.manage.dto.activity.response.event;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2025-04-11 14:42
 * @description： 会员事件奖品发放信息表
 * @modified By：
 * @version: $
 */
@Data
public class EventMemberRecord {

    @ExcelProperty(value = "事件类型", index = 0)
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ExcelProperty(value = "会员卡号", index = 1)
    @ApiModelProperty(value = "会员卡号")
    private String ffpCardNo;

    @ApiModelProperty(value = "许可key")
    private String licensesKey;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ExcelProperty(value = "发放奖品使用的规则/券码", index = 2)
    @ApiModelProperty(value = "发放奖品使用的规则/券码")
    private String couponCode;

    @ExcelProperty(value = "发放积分数/发放航段数", index = 3)
    @ApiModelProperty(value = "发放积分数/发放航段数")
    private String prizeNumber;

    @ExcelProperty(value = "发放状态", index = 4)
    @ApiModelProperty(value = "发放状态")
    private String provideStatusName;

    @ExcelProperty(value = "领取时间", index = 5)
    @ApiModelProperty(value = "领取时间")
    private String createTime;

}