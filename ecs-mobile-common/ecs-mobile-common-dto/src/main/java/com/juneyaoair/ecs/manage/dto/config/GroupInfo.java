package com.juneyaoair.ecs.manage.dto.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/14 15:12
 */
@Data
@JsonIgnoreProperties(value = {"groupItem"})
public class GroupInfo {
    @ApiModelProperty(value = "分组代码")
    private String groupCode;
    @ApiModelProperty(value = "分组名称")
    private String groupName;
    @ApiModelProperty(value = "分组明细")
    private List<Item> groupItem;
    @ApiModelProperty(value = "item明细")
    private List<String> itemDetail;
}
