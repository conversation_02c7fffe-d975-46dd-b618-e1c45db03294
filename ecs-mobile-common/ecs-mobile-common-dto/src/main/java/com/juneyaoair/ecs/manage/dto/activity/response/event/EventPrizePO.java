package com.juneyaoair.ecs.manage.dto.activity.response.event;

import com.juneyaoair.ecs.manage.dto.activity.common.DbBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date ：Created in 2025-04-11 14:42
 * @description： 事件奖品表
 * @modified By：
 * @version: $
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "T_EVENT_PRIZE")
public class EventPrizePO extends DbBase {

    @Id
    @ApiModelProperty(value = "奖品ID")
    private String eventPrizeId;

    @ApiModelProperty(value = "事件ID")
    private String eventInfoId;

    @ApiModelProperty(value = "组编码")
    private String groupCode;

    @ApiModelProperty(value = "奖品编码")
    private String prizeCode;

    @ApiModelProperty(value = "奖品类型 参照：PRIZE_TYPE_ENUM : 1:积分;2:优惠券;3:产品券;4:其他虚拟奖品;5:实物奖品,6:谢谢参与")
    private String prizeType;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "发放奖品使用的规则/券码")
    private String couponCode;

    @ApiModelProperty(value = "发放积分数/发放航段数")
    private Integer prizeNumber;

    @ApiModelProperty(value = "奖品总数量")
    private Integer prizeTotalCount;

    @ApiModelProperty(value = "奖品已发放数量")
    private Integer sendCount;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "事件状态 Y：有效 D:删除")
    private String status;

}