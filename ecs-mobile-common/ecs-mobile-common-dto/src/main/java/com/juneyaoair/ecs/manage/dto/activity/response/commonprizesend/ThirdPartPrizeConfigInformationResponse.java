package com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend;

/**
 * @ClassName IndefiniteQuantityResponse
 * @Description
 * <AUTHOR>
 * @Date 2025/4/1 12:41
 * @Version 1.0
 */
public class ThirdPartPrizeConfigInformationResponse {
    /**
     * <AUTHOR>
     * @Description 第三方奖品发送 活动信息
     * @Date 12:55 2025/4/1
     **/
    private ThirdPartActivityInformation thirdPartActivityInformation;

    /**
     * <AUTHOR>
     * @Description 第三方奖品发送 商户信息
     * @Date 12:55 2025/4/1
     **/
    private ThirdPartMerchantInformation thirdPartMerchantInformation;

    /**
     * <AUTHOR>
     * @Description 第三方奖品发送 奖品类型信息
     * @Date 12:55 2025/4/1
     **/
    private ThirdPartPrizeInformation thirdPartPrizeInformation;

    public ThirdPartPrizeConfigInformationResponse() {
    }

    public ThirdPartPrizeConfigInformationResponse(ThirdPartActivityInformation thirdPartActivityInformation, ThirdPartMerchantInformation thirdPartMerchantInformation, ThirdPartPrizeInformation thirdPartPrizeInformation) {
        this.thirdPartActivityInformation = thirdPartActivityInformation;
        this.thirdPartMerchantInformation = thirdPartMerchantInformation;
        this.thirdPartPrizeInformation = thirdPartPrizeInformation;
    }

    public ThirdPartActivityInformation getThirdPartActivityInformation() {
        return thirdPartActivityInformation;
    }

    public void setThirdPartActivityInformation(ThirdPartActivityInformation thirdPartActivityInformation) {
        this.thirdPartActivityInformation = thirdPartActivityInformation;
    }

    public ThirdPartMerchantInformation getThirdPartMerchantInformation() {
        return thirdPartMerchantInformation;
    }

    public void setThirdPartMerchantInformation(ThirdPartMerchantInformation thirdPartMerchantInformation) {
        this.thirdPartMerchantInformation = thirdPartMerchantInformation;
    }

    public ThirdPartPrizeInformation getThirdPartPrizeInformation() {
        return thirdPartPrizeInformation;
    }

    public void setThirdPartPrizeInformation(ThirdPartPrizeInformation thirdPartPrizeInformation) {
        this.thirdPartPrizeInformation = thirdPartPrizeInformation;
    }
}
