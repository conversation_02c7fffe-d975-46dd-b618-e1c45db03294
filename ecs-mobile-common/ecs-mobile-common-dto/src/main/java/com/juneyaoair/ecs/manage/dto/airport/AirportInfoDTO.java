package com.juneyaoair.ecs.manage.dto.airport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;


@ApiModel
public class AirportInfoDTO {
    /**
     *
     */
    @ApiModelProperty("")
    public String airportCode;

    /**
     *
     */
    @ApiModelProperty("")
    public String airportName;

    /**
     *
     */
    @ApiModelProperty("")
    public String airportEName;

    /**
     *
     */
    @ApiModelProperty("")
    public String cityCode;

    /**
     *
     */
    @ApiModelProperty("中文名简写")
    public String nameAbb;

    /**
     *
     */
    @ApiModelProperty("英文名简写")
    public String englishNameAbb;

    /**
     *
     */
    @ApiModelProperty("拼音简写")
    public String pinyinAbb;

    /**
     *
     */
    @ApiModelProperty("")
    public Date createDatetime;

    /**
     *
     */
    @ApiModelProperty("")
    public Long createId;

    /**
     *
     */
    @ApiModelProperty("")
    public String baiduMapPoint;

    /**
     *
     */
    @ApiModelProperty("")
    public String airportPinyin;

    /**
     * 网站
     */
    @ApiModelProperty("网站")
    public String website;

    /**
     * 航站楼（国内）
     */
    @ApiModelProperty("航站楼（国内）")
    public String terminalposition;

    /**
     * 值机柜台（国内）
     */
    @ApiModelProperty("值机柜台（国内）")
    public String checkincounter;

    /**
     * 头等舱值机柜台（国内）
     */
    @ApiModelProperty("头等舱值机柜台（国内）")
    public String firstclasscheckincounter;

    /**
     * 售票柜台（国内）
     */
    @ApiModelProperty("售票柜台（国内）")
    public String ticketcounter;

    /**
     * 值机开始时间
     */
    @ApiModelProperty("值机开始时间")
    public String checkinbegintime;

    /**
     * 值机结束时间
     */
    @ApiModelProperty("值机结束时间")
    public String checkinendtime;

    /**
     * 贵宾休息室（国内）
     */
    @ApiModelProperty("贵宾休息室（国内）")
    public String viproom;

    /**
     * 航站楼（国际）
     */
    @ApiModelProperty("航站楼（国际）")
    public String iTerminalposition;

    /**
     * 值机柜台（国际）
     */
    @ApiModelProperty("值机柜台（国际）")
    public String iCheckincounter;

    /**
     * 头等舱值机柜台（国际）
     */
    @ApiModelProperty("头等舱值机柜台（国际）")
    public String iFirstclasscheckincounter;

    /**
     * 贵宾休息室（国际）
     */
    @ApiModelProperty("贵宾休息室（国际）")
    public String iViproom;

    /**
     * 售票柜台（国际）
     */
    @ApiModelProperty("售票柜台（国际）")
    public String iTicketcounter;

    /**
     * 机场韩文名
     */
    @ApiModelProperty("机场韩文名")
    public String airportKoName;

    /**
     * 机场日文名
     */
    @ApiModelProperty("机场日文名")
    public String airportJpName;

    /**
     * 机场泰文名
     */
    @ApiModelProperty("机场泰文名")
    public String airportThName;

    /**
     * 机场繁体中文名
     */
    @ApiModelProperty("机场繁体中文名")
    public String airportTcName;

    @ApiModelProperty("Small Middle  Large")
    public String zonesLevel; //Small Middle  Large
    //登机口关闭时间
    @ApiModelProperty("登机口关闭时间")
    public String gateCloseDate;

    @ApiModelProperty(hidden = true)
    public String status;

    //经度
    @ApiModelProperty("经度")
    public String longitude;
    //纬度
    @ApiModelProperty("纬度")
    public String latitude;

    public AirportInfoDTO(String airportCode, String airportName, String airportEName, String cityCode, String nameAbb, String englishNameAbb, String pinyinAbb, Date createDatetime, Long createId, String baiduMapPoint, String airportPinyin, String website, String terminalposition, String checkincounter, String firstclasscheckincounter, String ticketcounter, String checkinbegintime, String checkinendtime, String viproom, String iTerminalposition, String iCheckincounter, String iFirstclasscheckincounter, String iViproom, String iTicketcounter, String airportKoName, String airportJpName, String airportThName, String airportTcName, String zonesLevel, String status, List<AirportLabelInfoDTO> listAirportLabel, List<AirportWarnDTO> listAirportWarn, String longitude, String latitude ) {
        this.airportCode = airportCode;
        this.airportName = airportName;
        this.airportEName = airportEName;
        this.cityCode = cityCode;
        this.nameAbb = nameAbb;
        this.englishNameAbb = englishNameAbb;
        this.pinyinAbb = pinyinAbb;
        this.createDatetime = createDatetime;
        this.createId = createId;
        this.baiduMapPoint = baiduMapPoint;
        this.airportPinyin = airportPinyin;
        this.website = website;
        this.terminalposition = terminalposition;
        this.checkincounter = checkincounter;
        this.firstclasscheckincounter = firstclasscheckincounter;
        this.ticketcounter = ticketcounter;
        this.checkinbegintime = checkinbegintime;
        this.checkinendtime = checkinendtime;
        this.viproom = viproom;
        this.iTerminalposition = iTerminalposition;
        this.iCheckincounter = iCheckincounter;
        this.iFirstclasscheckincounter = iFirstclasscheckincounter;
        this.iViproom = iViproom;
        this.iTicketcounter = iTicketcounter;
        this.airportKoName = airportKoName;
        this.airportJpName = airportJpName;
        this.airportThName = airportThName;
        this.airportTcName = airportTcName;
        this.zonesLevel = zonesLevel;
        this.status = status;
        this.listAirportLabel = listAirportLabel;
        this.listAirportWarn = listAirportWarn;
        this.longitude = longitude;
        this.latitude = latitude;
    }
    public String getZonesLevel() {
        return zonesLevel;
    }

    public void setZonesLevel(String zonesLevel) {
        this.zonesLevel = zonesLevel;
    }

    @ApiModelProperty(hidden = true)
    public List<AirportLabelInfoDTO> listAirportLabel;
    @ApiModelProperty(hidden = true)
    public List<AirportWarnDTO> listAirportWarn;

    public List<AirportWarnDTO> getListAirportWarn() {
        return listAirportWarn;
    }

    public void setListAirportWarn(List<AirportWarnDTO> listAirportWarn) {
        this.listAirportWarn = listAirportWarn;
    }

    public List<AirportLabelInfoDTO> getListAirportLabel() {
        return listAirportLabel;
    }

    public void setListAirportLabel(List<AirportLabelInfoDTO> listAirportLabel) {
        this.listAirportLabel = listAirportLabel;
    }
    public AirportInfoDTO() {
    }
    
    public String getAirportCode() {
        return airportCode;
    }
    
    public void setAirportCode(String airportCode) {
        this.airportCode = airportCode;
    }
    
    public String getAirportName() {
        return airportName;
    }
    
    public void setAirportName(String airportName) {
        this.airportName = airportName;
    }
    
    public String getAirportEName() {
        return airportEName;
    }
    
    public void setAirportEName(String airportEName) {
        this.airportEName = airportEName;
    }
    
    public String getCityCode() {
        return cityCode;
    }
    
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }
    
    public String getNameAbb() {
        return nameAbb;
    }
    
    public void setNameAbb(String nameAbb) {
        this.nameAbb = nameAbb;
    }
    
    public String getEnglishNameAbb() {
        return englishNameAbb;
    }
    
    public void setEnglishNameAbb(String englishNameAbb) {
        this.englishNameAbb = englishNameAbb;
    }
    
    public String getPinyinAbb() {
        return pinyinAbb;
    }
    
    public void setPinyinAbb(String pinyinAbb) {
        this.pinyinAbb = pinyinAbb;
    }
    
    public Date getCreateDatetime() {
        return createDatetime;
    }
    
    public void setCreateDatetime(Date createDatetime) {
        this.createDatetime = createDatetime;
    }
    
    public Long getCreateId() {
        return createId;
    }
    
    public void setCreateId(Long createId) {
        this.createId = createId;
    }
    
    public String getBaiduMapPoint() {
        return baiduMapPoint;
    }
    
    public void setBaiduMapPoint(String baiduMapPoint) {
        this.baiduMapPoint = baiduMapPoint;
    }
    
    public String getAirportPinyin() {
        return airportPinyin;
    }
    
    public void setAirportPinyin(String airportPinyin) {
        this.airportPinyin = airportPinyin;
    }
    
    public String getWebsite() {
        return website;
    }
    
    public void setWebsite(String website) {
        this.website = website;
    }
    
    public String getTerminalposition() {
        return terminalposition;
    }
    
    public void setTerminalposition(String terminalposition) {
        this.terminalposition = terminalposition;
    }
    
    public String getCheckincounter() {
        return checkincounter;
    }
    
    public void setCheckincounter(String checkincounter) {
        this.checkincounter = checkincounter;
    }
    
    public String getFirstclasscheckincounter() {
        return firstclasscheckincounter;
    }
    
    public void setFirstclasscheckincounter(String firstclasscheckincounter) {
        this.firstclasscheckincounter = firstclasscheckincounter;
    }
    
    public String getTicketcounter() {
        return ticketcounter;
    }
    
    public void setTicketcounter(String ticketcounter) {
        this.ticketcounter = ticketcounter;
    }
    
    public String getCheckinbegintime() {
        return checkinbegintime;
    }
    
    public void setCheckinbegintime(String checkinbegintime) {
        this.checkinbegintime = checkinbegintime;
    }
    
    public String getCheckinendtime() {
        return checkinendtime;
    }
    
    public void setCheckinendtime(String checkinendtime) {
        this.checkinendtime = checkinendtime;
    }
    
    public String getViproom() {
        return viproom;
    }
    
    public void setViproom(String viproom) {
        this.viproom = viproom;
    }
    
    public String getiTerminalposition() {
        return iTerminalposition;
    }
    
    public void setiTerminalposition(String iTerminalposition) {
        this.iTerminalposition = iTerminalposition;
    }
    
    public String getiCheckincounter() {
        return iCheckincounter;
    }
    
    public void setiCheckincounter(String iCheckincounter) {
        this.iCheckincounter = iCheckincounter;
    }
    
    public String getiFirstclasscheckincounter() {
        return iFirstclasscheckincounter;
    }
    
    public void setiFirstclasscheckincounter(String iFirstclasscheckincounter) {
        this.iFirstclasscheckincounter = iFirstclasscheckincounter;
    }
    
    public String getiViproom() {
        return iViproom;
    }
    
    public void setiViproom(String iViproom) {
        this.iViproom = iViproom;
    }
    
    public String getiTicketcounter() {
        return iTicketcounter;
    }
    
    public void setiTicketcounter(String iTicketcounter) {
        this.iTicketcounter = iTicketcounter;
    }
    
    public String getAirportKoName() {
        return airportKoName;
    }
    
    public void setAirportKoName(String airportKoName) {
        this.airportKoName = airportKoName;
    }
    
    public String getAirportJpName() {
        return airportJpName;
    }
    
    public void setAirportJpName(String airportJpName) {
        this.airportJpName = airportJpName;
    }
    
    public String getAirportThName() {
        return airportThName;
    }
    
    public void setAirportThName(String airportThName) {
        this.airportThName = airportThName;
    }
    
    public String getAirportTcName() {
        return airportTcName;
    }
    
    public void setAirportTcName(String airportTcName) {
        this.airportTcName = airportTcName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getGateCloseDate() {
        return gateCloseDate;
    }

    public void setGateCloseDate(String gateCloseDate) {
        this.gateCloseDate = gateCloseDate;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
}

