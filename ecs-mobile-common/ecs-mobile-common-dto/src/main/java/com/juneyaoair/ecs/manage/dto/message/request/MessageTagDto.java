package com.juneyaoair.ecs.manage.dto.message.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/11 14:22
 */
@Data
@ApiModel(value = "MessageTagDto", description = "公告标签请求对象")
public class MessageTagDto {
    @ApiModelProperty("标签id")
    private String mtid;

    @NotBlank(message = "标签名称不可为空")
    @ApiModelProperty("标签名称")
    private String mtname;

    @NotBlank(message = "标签配色不可为空")
    @ApiModelProperty("标签配色")
    private String mtcolor;

    @NotBlank(message = "标签背景配色不可为空")
    @ApiModelProperty("标签背景配色")
    private String mtbgcolor;
}
