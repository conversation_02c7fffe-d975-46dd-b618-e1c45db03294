package com.juneyaoair.ecs.manage.dto.airport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;


@ApiModel(description = "*/")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AirportWarnDTO {
    @ApiModelProperty("id")
    public String airportWarnId;
    @ApiModelProperty("开始or结束flag eg:S开始E结束")
    @NotNull
    public String depArrFlag;
    @ApiModelProperty("格式为yyyy-MM-dd - yyyy-MM-dd")
    @NotNull
    public String depShowDate;
    @ApiModelProperty("title")
    @NotNull
    public String depWarnTitle;
    @ApiModelProperty("排序")
    public String sortOrder;
    @ApiModelProperty("content")
    @NotNull
    public String depWarnContent;
}
