package com.juneyaoair.ecs.manage.dto.citymanage;

import lombok.Data;

import java.util.Date;

@Data
public class CityLabelInfoDTO {
    /**
     * 主键ID
     */
    public Integer cityLabelId;

    /**
     * 城市三字码
     */
    public String cityCode;

    /**
     * 城市标签名
     */
    public String cityLabelName;
    /**
     * 城市标签描述
     */
    public String labelIntroduce;

    /**
     * 城市标签图片url
     */
    public String cityLabelUrl;
    /**
     * 创建时间
     */
    public Date createtime;
    /**
     * 创建人
     */
    public String createUser;
    /**
     * 更新时间
     */
    public Date updatetime;
    /**
     * 更新人
     */
    public String updateUser;
}
