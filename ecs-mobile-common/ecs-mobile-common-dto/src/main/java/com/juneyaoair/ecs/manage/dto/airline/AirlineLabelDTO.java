package com.juneyaoair.ecs.manage.dto.airline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class AirlineLabelDTO {
    /**
     *
     */
    @ApiModelProperty("航线id")
    public String airlineId;

    /**
     *
     */
    @ApiModelProperty("序号")
    public String sequence;

    /**
     *
     */
    @ApiModelProperty("标签类型")
    public String labelType;

    /**
     *
     */
    @ApiModelProperty("标签名称")
    public String labelName;

    /**
     *
     */
    @ApiModelProperty("url")
    public String url;

    /**
     *
     */
    @ApiModelProperty("id")
    public String id;
}

