package com.juneyaoair.ecs.manage.dto.activity.redeem.segact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

@Data
public class SegActAwardDTO {

    @NotNull
    @ApiModelProperty(value = "奖品名称", allowableValues = "", notes = "", example = "")
    private String awardName;

    @NotNull
    @ApiModelProperty(value = "奖品描述", allowableValues = "", notes = "", example = "")
    private String awardDesc;

    @NotNull
    @ApiModelProperty(value = "每月限制", allowableValues = "", notes = "", example = "")
    private String monthlyLimit;

    @ApiModelProperty(value = "飞行券", allowableValues = "", notes = "", example = "")
    @Positive
    private Integer pointCount;

    @ApiModelProperty(value = "飞行券Pro", allowableValues = "", notes = "", example = "")
    @Positive
    private Integer proPointCount;

    @NotNull
    @ApiModelProperty(value = "奖池编码", allowableValues = "", notes = "", example = "")
    private String prizePoolCode;

}
