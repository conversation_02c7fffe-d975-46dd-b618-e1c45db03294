package com.juneyaoair.ecs.manage.dto.I18n;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateI18nDictionaryKVRequestDTO", description = "更新字典KV请求参数")
public class UpdateI18nDictionaryKVRequestDTO {

    @NotNull
    @ApiModelProperty(value = "字典值ID", required = true, example = "87654321-4321-8765-4321-876543210123")
    private String id;

    @ApiModelProperty(value = "KEY;原始值", required = true)
    private String key;

    @ApiModelProperty(value = "语言标签", required = true, example = "ZH_CN")
    private String languageTag;

    @ApiModelProperty(value = "翻译内容", required = true)
    private String translation;

    @ApiModelProperty(value = "备注")
    private String remark;
} 