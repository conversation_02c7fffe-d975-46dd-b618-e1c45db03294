package com.juneyaoair.ecs.manage.dto.citymanage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CityManageDTO {

    /**
     *
     */
    @ApiModelProperty("")
    public String cityCode;

    /**
     *
     */
    @ApiModelProperty("")
    public String cityName;

    /**
     *
     */
    @ApiModelProperty("")
    public String cityEName;

    /**
     *
     */
    @ApiModelProperty("")
    public String countryCode;

    /**
     *
     */
    @ApiModelProperty("")
    public String cityPinYin;

    /**
     *
     */
    @ApiModelProperty("")
    public String cityPinYinAbb;

    /**
     *
     */
    @ApiModelProperty("")
    public Integer provinceId;

    /**
     *
     */
    @ApiModelProperty("")
    public String provinceName;

    /**
     *
     */
    @ApiModelProperty("")
    public String isHotCity;

    @ApiModelProperty("")
    public String isHotRegion;

    /**
     *
     */
    @ApiModelProperty("")
    public String isTopCity;

    /**
     *
     */
    @ApiModelProperty("")
    public String officeAddress;

    /**
     *
     */
    @ApiModelProperty("")
    public String officeTel;

    /**
     *
     */
    @ApiModelProperty("")
    public Date createDatetime;

    /**
     *
     */
    @ApiModelProperty("")
    public Long createId;

    /**
     *
     */
    @ApiModelProperty("")
    public String cityTimeZone;

    /**
     *
     */
    @ApiModelProperty("")
    public String nameAbb;

    /**
     *
     */
    @ApiModelProperty("")
    public String englishNameAbb;

    /**
     *
     */
    @ApiModelProperty("")
    public String isInternational;

    /**
     *
     */
    @ApiModelProperty("")
    public String baidumappoint;

    /**
     *
     */
    @ApiModelProperty("")
    public String officeFax;

    /**
     *
     */
    @ApiModelProperty("")
    public String delflag;

    /**
     *
     */
    @ApiModelProperty("")
    public String url;

    /**
     * 热门城市排序
     */
    @ApiModelProperty("热门城市排序")
    public Short cityHotOrder;

    @ApiModelProperty("热门地区排序")
    public Short zoneHotOrder;

    /**
     * icon灏忓浘鏍囧湴鍧€
     */
    @ApiModelProperty("icon灏忓浘鏍囧湴鍧€")
    public String iconUrl;

    /**
     * 澶忎护鏃躲€佸啲浠ゆ椂璇︽儏
     */
    @ApiModelProperty("澶忎护鏃躲€佸啲浠ゆ椂璇︽儏")
    public String dstWtId;

    /**
     * 韩文城市名
     */
    @ApiModelProperty("韩文城市名")
    public String cityKoName;

    /**
     * 日文城市名
     */
    @ApiModelProperty("日文城市名")
    public String cityJpName;

    /**
     * 泰文城市名
     */
    @ApiModelProperty("泰文城市名")
    public String cityThName;

    /**
     * 繁体中文
     */
    @ApiModelProperty("繁体中文")
    public String cityTcName;
    
    
    /**
     * 上传图片信息
     */
    @ApiModelProperty("上传图片信息(非pc端)")
    public FileInfoDTO fileInfo;

    /**
     * 关键字 2020-3-19
     */
    @ApiModelProperty("关键字")
    public String cityKeyWords;

    @ApiModelProperty("0:禁用 1:启用")
    public String status; // 0:禁用 1:启用

    //经度
    @ApiModelProperty("经度")
    public String longitude;
    //纬度
    @ApiModelProperty("纬度")
    public String latitude;

    /**
     * pc端图片url
     */
    @ApiModelProperty("pc端图片url")
    public String cityPicturePcUrl;

    @ApiModelProperty(value = "邻近机场")
    public String nearbyAirport;
}
