package com.juneyaoair.ecs.manage.dto.activity.response;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

public class FileInfo implements Serializable {
	
	private static final long serialVersionUID = -3381491258049895478L;
	
	private String fileId;  //主键
	private String linkId;  //文件所属模块对应表主键
	private String fileName;  //文件名
	private String fileURL;  //文件路径
	private String fileSize;  //文件大小
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;  //文件创建时间
	private String creatorId;  //创建人
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;  //文件最后一次修改时间
	private String realName;
	private String imgServiceUrl;//图片服务器地址
	private String serverPath;
	private String hostName;
	private String filePath;
	private String fileType;//对应图片种类;ICON对应分享图片，INDEX对应移动端首页显示图,VIDEO对应视频
	private String fileMd5;//文件MD5检验

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public FileInfo() {
		super();
	}
	
	public FileInfo(String fileId, String linkId, String fileName,
                    String fileURL, String fileSize, Date createTime, String creatorId,
                    Date modifyTime) {
		super();
		this.fileId = fileId;
		this.linkId = linkId;
		this.fileName = fileName;
		this.fileURL = fileURL;
		this.fileSize = fileSize;
		this.createTime = createTime;
		this.creatorId = creatorId;
		this.modifyTime = modifyTime;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	public String getLinkId() {
		return linkId;
	}

	public void setLinkId(String linkId) {
		this.linkId = linkId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileURL() {
		return fileURL;
	}

	public void setFileURL(String fileURL) {
		this.fileURL = fileURL;
	}

	public String getFileSize() {
		return fileSize;
	}

	public void setFileSize(String fileSize) {
		this.fileSize = fileSize;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	@Override
	public String toString() {
		return "FileInfo [fileId=" + fileId + ", linkId=" + linkId
				+ ", fileName=" + fileName + ", fileURL=" + fileURL
				+ ", fileSize=" + fileSize + ", createTime=" + createTime
				+ ", creatorId=" + creatorId + ", modifyTime=" + modifyTime
				+ "]";
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getImgServiceUrl() {
		return imgServiceUrl;
	}

	public void setImgServiceUrl(String imgServiceUrl) {
		this.imgServiceUrl = imgServiceUrl;
	}

	public String getServerPath() {
		return serverPath;
	}

	public void setServerPath(String serverPath) {
		this.serverPath = serverPath;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getHostName() {
		return hostName;
	}

	public void setHostName(String hostName) {
		this.hostName = hostName;
	}

	public String getFileMd5() {
		return fileMd5;
	}

	public void setFileMd5(String fileMd5) {
		this.fileMd5 = fileMd5;
	}
}
