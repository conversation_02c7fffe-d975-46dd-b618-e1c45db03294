package com.juneyaoair.ecs.manage.dto.avcabin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/8 13:09
 */
@Data
@ApiModel("航班舱位信息返回对象")
public class AvCabinDto {
    @ApiModelProperty("航班日期")
    private String flightDate;

    @ApiModelProperty("承运航班号")
    private String carrier;

    @ApiModelProperty("舱位明细列表")
    private List<CabinData> cabinDateList;
}
