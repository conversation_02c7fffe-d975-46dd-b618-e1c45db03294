package com.juneyaoair.ecs.manage.enums;

/**
 * @ClassName CommonCouponPrizeStatusEnum
 * @Description 通用奖品发送状态枚举
 * <AUTHOR>
 * @Date 2025/3/31 11:13
 * @Version 1.0
 */
public enum CommonCouponPrizeStatusEnum {
    INIT("INIT", "初始化"),
    PREPARE("PREPARE", "预发送"),
    SUCCESS("SUCCESS", "发送成功"),
    FAILED("FAILED", "发送失败");

    private final String code;

    private final String desc;

    CommonCouponPrizeStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
