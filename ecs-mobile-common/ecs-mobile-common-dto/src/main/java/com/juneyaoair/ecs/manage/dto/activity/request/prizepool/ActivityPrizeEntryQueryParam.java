package com.juneyaoair.ecs.manage.dto.activity.request.prizepool;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: caolei
 * @Description: 活动奖池信息表查询
 * @Date: 2024/07/09 9:15
 * @Modified by:
 */
@Data
public class ActivityPrizeEntryQueryParam {

    @NotBlank(message = "奖池编码不能为空")
    @ApiModelProperty(value = "奖池编码", required = true)
    private String prizePoolCode;

}
