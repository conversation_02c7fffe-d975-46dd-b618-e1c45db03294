package com.juneyaoair.ecs.manage.dto.avcabin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/8 13:13
 */
@Data
@ApiModel("舱位数量")
public class CabinData {
    @ApiModelProperty("子舱位代码")
    private String cabinCode;
    @ApiModelProperty("子舱位剩余数量")
    private String num;

    /**
     * 从舱位列表获取剩余数量
     * @param cabins
     */
    public void setNum(String[] cabins){
        for (int i = 0; i < cabins.length; i++) {
            String cabin = cabins[i];
            String cabinCode = cabin.substring(0, cabin.indexOf(","));
            if(this.cabinCode.equals(cabinCode)){
                this.num = cabin.substring(cabinCode.length() + 1);
            }
        }
    }
}
