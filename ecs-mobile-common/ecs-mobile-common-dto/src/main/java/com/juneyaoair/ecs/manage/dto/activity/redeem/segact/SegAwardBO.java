package com.juneyaoair.ecs.manage.dto.activity.redeem.segact;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SegAwardBO {

    @ApiModelProperty(value = "id", allowableValues = "", notes = "", example = "")
    private String id;

    @ApiModelProperty(value = "奖品名称", allowableValues = "", notes = "", example = "")
    private String awardName;

    @ApiModelProperty(value = "奖品描述", allowableValues = "", notes = "", example = "")
    private String awardDesc;

    @ApiModelProperty(value = "每月限制", allowableValues = "", notes = "", example = "")
    private String monthlyLimit;

    @ApiModelProperty(value = "所需飞行券", allowableValues = "", notes = "", example = "")
    private String pointCount;

    @ApiModelProperty(value = "所需飞行券Pro", allowableValues = "", notes = "", example = "")
    private String proPointCount;

    @ApiModelProperty(value = "奖池编码", allowableValues = "", notes = "", example = "")
    private String prizePoolCode;

    @JsonIgnore
    private String consumedPointType;

    @JsonIgnore
    private String consumedPoint;

}
