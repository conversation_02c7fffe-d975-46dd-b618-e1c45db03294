package com.juneyaoair.ecs.manage.dto.specialAirline;

import com.juneyaoair.ecs.manage.dto.base.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025-01-26
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SpecialAirlineQueryParam extends PageBase {

    @ApiModelProperty(value = "出发城市三字码")
    private String depCityCode;

    @ApiModelProperty(value = "到达城市三字码")
    private String arrCityCode;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

}
