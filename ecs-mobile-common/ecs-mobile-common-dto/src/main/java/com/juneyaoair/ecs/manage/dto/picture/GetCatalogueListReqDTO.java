package com.juneyaoair.ecs.manage.dto.picture;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel
public class GetCatalogueListReqDTO {
    @ApiModelProperty(hidden = false)
    public Long curIndexId;

    /**
     * 类型 0：root根; 1：index目录; 2：node节点
     */
    @ApiModelProperty("类型 0：root根; 1：index目录; 2：node节点")
    public Integer type;

    /**
     * node节点图片location
     */
    @ApiModelProperty("node节点图片location")
    public String picLocation;

    /**
     * 父级层级
     */
    @ApiModelProperty("父级层级")
    public Long parentId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    public String name;

    /**
     * 是否删除 0：未删除；1：已删除
     */
    @ApiModelProperty("是否删除 0：未删除；1：已删除")
    public String deletedFlag;

    /**
     *
     */
    @ApiModelProperty("")
    public String createUser;

    /**
     *
     */
    @ApiModelProperty("")
    public Date updateTime;

    /**
     *
     */
    @ApiModelProperty("")
    public String updateUser;
    @ApiModelProperty(hidden = false)
    public Date createTime;
}
