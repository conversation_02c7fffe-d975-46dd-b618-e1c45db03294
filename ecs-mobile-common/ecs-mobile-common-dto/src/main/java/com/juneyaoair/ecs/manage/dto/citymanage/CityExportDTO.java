package com.juneyaoair.ecs.manage.dto.citymanage;

import lombok.Data;

import java.util.Date;

@Data
public class CityExportDTO {
    /**
     *
     */
    public String cityCode;

    /**
     *
     */
    public String cityName;

    /**
     *
     */
    public String cityEName;

    /**
     *
     */
    public String countryCode;

    /**
     *
     */
    public String cityPinYin;

    /**
     *
     */
    public String cityPinYinAbb;

    /**
     *
     */
    public Integer provinceId;

    /**
     *
     */
    public String provinceName;

    /**
     *
     */
    public String isHotCity;

    /**
     *
     */
    public String isTopCity;

    /**
     *
     */
    public String officeAddress;

    /**
     *
     */
    public String officeTel;

    /**
     *
     */
    public Date createDatetime;

    /**
     *
     */
    public Long createId;

    /**
     *
     */
    public String cityTimeZone;

    /**
     *
     */
    public String nameAbb;

    /**
     *
     */
    public String englishNameAbb;

    /**
     *
     */
    public String isInternational;

    /**
     *
     */
    public String baidumappoint;

    /**
     *
     */
    public String officeFax;

    /**
     *
     */
    public String delflag;

    /**
     *
     */
    public String url;

    /**
     * 鐑棬鎺掑簭
     */
    public Integer cityHotOrder;

    /**
     * icon灏忓浘鏍囧湴鍧€
     */
    public String iconUrl;

    /**
     * 澶忎护鏃躲€佸啲浠ゆ椂璇︽儏
     */
    public String dstWtId;

    /**
     * 韩文城市名
     */
    public String cityKoName;

    /**
     * 日文城市名
     */
    public String cityJpName;

    /**
     * 泰文城市名
     */
    public String cityThName;

    /**
     * 繁体中文
     */
    public String cityTcName;

    /**
     * 城市标签
     */
    public String cityLabelName;


    /**
     * 夏令时
     */
    public String dstWtInfo;


    /**
     * 关键字 2020-3-19
     */
    public String cityKeyWords;
    /**
     * 是否热门地区 2020-12-09
     */
    public String isHotRegion;

    public String status; // 0:禁用 1:启用
    //经度
    public String longitude;
    //纬度
    public String latitude;
    /**
     * 是否常用城市
     */
    public String isOftenCity;
    /**
     * pc端城市图片
     */
    public String cityPicturePcUrl;
}
