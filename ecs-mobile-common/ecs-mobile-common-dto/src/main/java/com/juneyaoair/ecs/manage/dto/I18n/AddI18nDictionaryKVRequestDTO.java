package com.juneyaoair.ecs.manage.dto.I18n;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@ApiModel(value = "AddI18nDictionaryKVRequestDTO", description = "新增/更新字典KV请求参数")
@NoArgsConstructor
@AllArgsConstructor
public class AddI18nDictionaryKVRequestDTO {

    @NotNull
    @ApiModelProperty(value = "字典ID", required = true)
    private String dictionaryId;

    @ApiModelProperty(value = "KEY;原始值", required = true)
    private String key;

    @ApiModelProperty(value = "语言标签", required = true, example = "ZH_CN")
    private String languageTag;

    @ApiModelProperty(value = "翻译内容", required = true)
    private String translation;

    @ApiModelProperty(value = "备注")
    private String remark;
}
