package com.juneyaoair.ecs.manage.dto.datadict;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @Description 数据字典类型实体类对应数据库表TFB_DICTTYPE
 * @Date 14:39 2024/1/23
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DICTType {
    @ApiModelProperty(value = "类型id主键")
    private String dtid;

    @ApiModelProperty(value = "编码")
    private String dtCode;

    @ApiModelProperty(value = "名称")
    private String dtName;

    @ApiModelProperty(value = "描述")
    private String dtDescription;

    @ApiModelProperty(value = "是否有效")
    private String enable;
}
