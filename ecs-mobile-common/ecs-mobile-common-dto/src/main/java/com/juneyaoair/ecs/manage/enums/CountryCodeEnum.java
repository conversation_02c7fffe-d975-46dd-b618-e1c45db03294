package com.juneyaoair.ecs.manage.enums;

public enum CountryCodeEnum {
    TW("TW", "TW"),
    US("US", "US"),
    TR("TR", "TR"),
    LT("LT", "LT"),
    SG("SG", "SG"),
    IS("IS", "IS"),
    PK("PK", "PK"),
    CZ("CZ", "CZ"),
    CH("CH", "CH"),
    PH("PH", "PH"),
    ES("ES", "ES"),
    BE("BE", "BE"),
    EE("EE", "EE"),
    IT("IT", "IT"),
    FR("FR", "FR"),
    VN("VN", "VN"),
    PE("PE", "PE"),
    FI("FI", "FI"),
    MY("MY", "MY"),
    KR("KR", "KR"),
    HR("HR", "HR"),
    GR("GR", "GR"),
    KH("KH", "KH"),
    MM("MM", "MM"),
    HU("HU", "HU"),
    DK("DK", "DK"),
    IE("IE", "IE"),
    SE("SE", "SE"),
    NO("NO", "NO"),
    GA("GA", "GA"),
    LV("LV", "LV"),
    PT("PT", "PT"),
    CN("CN", "CN"),
    HK("HK", "HK"),
    DE("DE", "DE"),
    RU("RU", "RU"),
    RO("RO", "RO"),
    GB("GB", "GB"),
    AT("AT", "AT"),
    MO("MO", "MO"),
    SA("SA", "SA"),
    ID("ID", "ID"),
    LA("LA", "LA"),
    PL("PL", "PL"),
    JP("JP", "JP"),
    IN("IN", "IN"),
    TH("TH", "TH"),
    NL("NL", "NL"),
    KZ("KZ", "KZ"),
    SK("SK", "SK"),
    NONE("none", "未知");

    private final String code;
    private final String desc;

    CountryCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
