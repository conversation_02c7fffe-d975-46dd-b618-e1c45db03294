package com.juneyaoair.ecs.manage.dto.citymanage;

import com.juneyaoair.ecs.manage.dto.country.CountryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@ApiModel
@Data
public class CityInfoRespDTO {

    @ApiModelProperty("")
    public String cityCode;

    @ApiModelProperty("")
    public String cityName;

    @ApiModelProperty("")
    public String cityEName;

    @ApiModelProperty("")
    public String countryCode;

    @ApiModelProperty("")
    public String cityPinYin;

    @ApiModelProperty("")
    public String cityPinYinAbb;

    @ApiModelProperty("")
    public BigDecimal provinceId;

    @ApiModelProperty("")
    public String provinceName;

    @ApiModelProperty("")
    public String isHotCity;

    @ApiModelProperty("")
    public String isTopCity;

    @ApiModelProperty("")
    public String officeAddress;

    @ApiModelProperty("")
    public String officeTel;

    @ApiModelProperty("")
    public Date createDatetime;

    @ApiModelProperty("")
    public Long createId;

    @ApiModelProperty("")
    public String cityTimeZone;

    @ApiModelProperty(value = "名称简称")
    public String nameAbb;

    @ApiModelProperty("")
    public String englishNameAbb;

    @ApiModelProperty("")
    public String isInternational;

    @ApiModelProperty("")
    public String baidumappoint;

    @ApiModelProperty("")
    public String officeFax;

    @ApiModelProperty("")
    public String delflag;

    @ApiModelProperty("")
    public String url;

    /**
     * 热门城市排序
     */
    @ApiModelProperty("cityHotOrder")
    public BigDecimal cityHotOrder;

    @ApiModelProperty("热门地区排序")
    public BigDecimal zoneHotOrder;

    @ApiModelProperty(value = "icon小图片")
    public String iconUrl;

    /**
     * 夏令时id
     */
    @ApiModelProperty("夏令时id")
    public String dstWtId;

    /**
     * 韩文城市名
     */
    @ApiModelProperty("韩文城市名")
    public String cityKoName;

    /**
     * 日文城市名
     */
    @ApiModelProperty("日文城市名")
    public String cityJpName;

    /**
     * 泰文城市名
     */
    @ApiModelProperty("泰文城市名")
    public String cityThName;

    /**
     * 繁体中文
     */
    @ApiModelProperty("繁体中文")
    public String cityTcName;

    /**
     * 城市关键字
     */
    @ApiModelProperty(value = "城市关键字")
    public String cityKeyWords;

    /**
     * 是否热门地区: Y:热门地区  N:非热门地区
     */
    @ApiModelProperty("是否热门地区: Y:热门地区  N:非热门地区")
    public String isHotRegion;

    /**
     * 0:禁用  1:启用
     */
    @ApiModelProperty("0:禁用  1:启用")
    public String status;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    public String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    public String latitude;

    /**
     * pc端图片url
     */
    @ApiModelProperty("pc端图片url")
    public String cityPicturePcUrl;
    /**
     * 常用城市
     */
    @ApiModelProperty("常用城市")
    public String isOftenCity;

    @ApiModelProperty(hidden = true)
    public FileInfoDTO fileInfo;
    @ApiModelProperty
    public List<CityLabelInfoDTO> listCityLabel;

    @ApiModelProperty
    public DstWtInfoDTO dstWtInfo;

    @ApiModelProperty
    public CityEpidemicDTO cityEpidemic;

    @ApiModelProperty
    public List<CityWarnDTO> cityWarnDTO;

    @ApiModelProperty
    public CountryDTO countryDTO;

    @ApiModelProperty(value = "邻近机场")
    public String nearbyAirport;
}
