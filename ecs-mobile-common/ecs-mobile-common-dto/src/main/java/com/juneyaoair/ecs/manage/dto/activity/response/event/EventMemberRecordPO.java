package com.juneyaoair.ecs.manage.dto.activity.response.event;

import com.juneyaoair.ecs.manage.dto.activity.common.DbBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date ：Created in 2025-04-11 14:42
 * @description： 会员事件奖品发放信息表
 * @modified By：
 * @version: $
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "T_EVENT_MEMBER_RECORD")
public class EventMemberRecordPO extends DbBase {

    @Id
    @ApiModelProperty(value = "数据ID")
    private String recordId;

    @ApiModelProperty(value = "事件ID")
    private String eventInfoId;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "会员卡号")
    private String ffpCardNo;

    @ApiModelProperty(value = "许可key")
    private String licensesKey;

    @ApiModelProperty(value = "奖品ID")
    private String eventPrizeId;

    @ApiModelProperty(value = "奖品类型 参照：PRIZE_TYPE_ENUM : 1:积分;2:优惠券;3:产品券;4:其他虚拟奖品;5:实物奖品,6:谢谢参与")
    private String prizeType;

    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    @ApiModelProperty(value = "发放奖品使用的规则/券码")
    private String couponCode;

    @ApiModelProperty(value = "发放积分数/发放航段数")
    private Integer prizeNumber;

    @ApiModelProperty(value = "是否发放奖品(D:已发放；DS:发放中；DF:发放失败；F:未发放 UN:未获得)")
    private String provideStatus;

}