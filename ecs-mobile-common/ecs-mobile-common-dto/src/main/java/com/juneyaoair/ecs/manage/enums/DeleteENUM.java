package com.juneyaoair.ecs.manage.enums;

/**
 * @ClassName DELENUM
 * @Description 是否删除标记 注意：N-表示已删除 Y则反之
 * <AUTHOR>
 * @Date 2024/6/13 9:25
 * @Version 1.0
 */
public enum DeleteENUM {
    DELETED("N", "已删除"),
    UN_DELETED("Y", "未删除"),
    ;


    public final String code;
    public final String desc;

    DeleteENUM(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
