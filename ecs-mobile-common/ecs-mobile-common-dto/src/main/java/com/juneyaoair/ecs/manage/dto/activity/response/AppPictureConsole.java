package com.juneyaoair.ecs.manage.dto.activity.response;
import java.util.List;

public class AppPictureConsole {
    private String picId;
    private String startTime;
    private String endTime;
    private String picUrl;
    private String title;
    private String description;
    private String descriptionPlainTxt;
    private String status;
    private String statusCode;
    private String updateMan;
    private String updateTime;
    private String createTime;
    private String createMan;
    private String url;
    //保存時文件的ID
    private String[] fileID;
    private List<FileInfo> fileInfos;
    private String isLogin;//判断是否需要登录
    private String modeType; //图片发布模式

    private String isShared; //是否可分享
    private String shareIconUrl;//缩略图
    private String shareDesc;//分享描述
    private String isGiftPoints;//是否送积分
    private String isGiftCoupons;//是否送优惠券
    private String mobileModel;
    private String displayTime;//展示时长
    public String getIsShared() {
        return isShared;
    }

    public void setIsShared(String isShared) {
        this.isShared = isShared;
    }

    public String getShareIconUrl() {
        return shareIconUrl;
    }

    public void setShareIconUrl(String shareIconUrl) {
        this.shareIconUrl = shareIconUrl;
    }

    public String getShareDesc() {
        return shareDesc;
    }

    public void setShareDesc(String shareDesc) {
        this.shareDesc = shareDesc;
    }

    public String getIsGiftPoints() {
        return isGiftPoints;
    }

    public void setIsGiftPoints(String isGiftPoints) {
        this.isGiftPoints = isGiftPoints;
    }

    public String getIsGiftCoupons() {
        return isGiftCoupons;
    }

    public void setIsGiftCoupons(String isGiftCoupons) {
        this.isGiftCoupons = isGiftCoupons;
    }

    public String getPicId() {
        return picId;
    }

    public void setPicId(String picId) {
        this.picId = picId;
    }


    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUpdateMan() {
        return updateMan;
    }

    public void setUpdateMan(String updateMan) {
        this.updateMan = updateMan;
    }

    public String getCreateMan() {
        return createMan;
    }

    public void setCreateMan(String createMan) {
        this.createMan = createMan;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String[] getFileID() {
        return fileID;
    }

    public void setFileID(String[] fileID) {
        this.fileID = fileID;
    }

    public List<FileInfo> getFileInfos() {
        return fileInfos;
    }

    public void setFileInfos(List<FileInfo> fileInfos) {
        this.fileInfos = fileInfos;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getDescriptionPlainTxt() {
        return descriptionPlainTxt;
    }

    public void setDescriptionPlainTxt(String descriptionPlainTxt) {
        this.descriptionPlainTxt = descriptionPlainTxt;
    }

    public String getIsLogin() {
        return isLogin;
    }

    public void setIsLogin(String isLogin) {
        this.isLogin = isLogin;
    }

    public String getModeType() {
        return modeType;
    }

    public void setModeType(String modeType) {
        this.modeType = modeType;
    }

    public String getMobileModel() {
        return mobileModel;
    }

    public void setMobileModel(String mobileModel) {
        this.mobileModel = mobileModel;
    }

    public String getDisplayTime() {
        return displayTime;
    }

    public void setDisplayTime(String displayTime) {
        this.displayTime = displayTime;
    }
}
