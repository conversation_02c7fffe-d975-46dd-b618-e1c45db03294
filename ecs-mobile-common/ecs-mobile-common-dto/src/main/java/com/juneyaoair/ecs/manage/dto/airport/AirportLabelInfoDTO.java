package com.juneyaoair.ecs.manage.dto.airport;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AirportLabelInfoDTO {
    /**
     * 主键ID
     */
    public Integer airportLabelId;

    /**
     * 机场三字码
     */
    public String airportCode;

    /**
     * 机场标签名
     */
    public String airportLabelName;

    /**
     * 机场图片标签url
     */
    public String airportLabelUrl;

    /**
     * 更新时间
     */
    public Date updatetime;

    /**
     * 更新人
     */
    public String updateUser;

    /**
     * 创建人
     */
    public String createUser;

    /**
     * 创建时间
     */
    public Date createtime;

    /**
     * 机场标签介绍url
     */
    public String labelIntroduce;
}

