package com.juneyaoair.ecs.manage.dto.datadict;


public class VersionManage {
    private Integer versionID;
    private String channelCode;
    private String versionType;
    private String versionNo;
    private String ios;
    private String android;
    private String iosVersion;
    private String andVersion;
    private String updateDetails;

    public String getUpdateDetails() {
        return updateDetails;
    }

    public void setUpdateDetails(String updateDetails) {
        this.updateDetails = updateDetails;
    }

    public Integer getVersionID() {
        return versionID;
    }

    public void setVersionID(Integer versionID) {
        this.versionID = versionID;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getVersionType() {
        return versionType;
    }

    public void setVersionType(String versionType) {
        this.versionType = versionType;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getIos() {
        return ios;
    }

    public void setIos(String ios) {
        this.ios = ios;
    }


    public String getAndroid() {
        return android;
    }

    public void setAndroid(String android) {
        this.android = android;
    }


    public String getIosVersion() {
        return iosVersion;
    }

    public void setIosVersion(String iosVersion) {
        this.iosVersion = iosVersion;
    }

    public String getAndVersion() {
        return andVersion;
    }

    public void setAndVersion(String andVersion) {
        this.andVersion = andVersion;
    }
}
