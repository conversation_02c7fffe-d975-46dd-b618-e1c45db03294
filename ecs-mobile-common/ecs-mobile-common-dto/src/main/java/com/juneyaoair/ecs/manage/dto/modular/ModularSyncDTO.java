package com.juneyaoair.ecs.manage.dto.modular;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ModularSyncDTO", description = "服务配置版本同步信息")
public class ModularSyncDTO {
    @ApiModelProperty("iOS版本")
    private String ios;

    @ApiModelProperty("安卓版本")
    private String android;

    @ApiModelProperty("鸿蒙版本")
    private String harmony;

    @ApiModelProperty("新iOS版本")
    private String newIos;

    @ApiModelProperty("新安卓版本")
    private String newAndroid;

    @ApiModelProperty("新鸿蒙版本")
    private String newHarmony;
} 