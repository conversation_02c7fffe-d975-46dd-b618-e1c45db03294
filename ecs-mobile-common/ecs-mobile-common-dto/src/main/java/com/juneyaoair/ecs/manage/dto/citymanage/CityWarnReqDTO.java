package com.juneyaoair.ecs.manage.dto.citymanage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CityWarnReqDTO {
    @ApiModelProperty("")
    public String id;
    @ApiModelProperty("格式需要是：yyyy-mm-dd - yyyy-mm-dd")
    public String depShowDate;
    @ApiModelProperty("")
    public String depWarnTitle;
    @ApiModelProperty("")
    public String depWarnContent;
    @ApiModelProperty("格式需要是：yyyy-mm-dd - yyyy-mm-dd")
    public String arrShowDate;
    @ApiModelProperty("")
    public String arrWarnTitle;
    @ApiModelProperty("")
    public String arrWarnContent;
    /**
     * 出发城市航线类型  I 国际  D 国内
     */
    @ApiModelProperty("出发城市航线类型  I 国际  D 国内")
    public String  depRouteType;

    /**
     * 到达城市航线类型  I 国际  D 国内
     */
    @ApiModelProperty("到达城市航线类型  I 国际  D 国内")
    public String arrRouteType;

}
