package com.juneyaoair.ecs.manage.dto.flight.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("航班信息传输对象")
public class FightResponseDto {

    @ApiModelProperty("记录主键")
    private Integer id;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("航班日期")
    private String flightDate;

    @ApiModelProperty("航班出发日期")
    private String flightDateOrg;

    @ApiModelProperty("航班到达日期")
    private String flightArrDate;

    @ApiModelProperty("英文航班日期")
    private String flightDateEn;

    @ApiModelProperty("出发城市")
    private String depCity;

    @ApiModelProperty("出发机场")
    private String depAirport;

    @ApiModelProperty("出发时刻")
    private String depDateTime;

    @ApiModelProperty("出发时间时区")
    private String depDateLocaltimezone;

    @ApiModelProperty("北京出发时间")
    private String depDateChinatime;

    @ApiModelProperty("出发航站楼")
    private String depAirportTerminal;

    @ApiModelProperty("到达城市")
    private String arrCity;

    @ApiModelProperty("到达机场")
    private String arrAirport;

    @ApiModelProperty("到达时刻")
    private String arrDateTime;

    @ApiModelProperty("到达时间时区")
    private String arrDateLocaltimezone;

    @ApiModelProperty("北京到达时间")
    private String arrDateChinatime;

    @ApiModelProperty("到达航站楼")
    private String arrAirportTerminal;

    @ApiModelProperty("是否主航班")
    private String isMain;

    @ApiModelProperty("是否经停航班")
    private String isStop;

    @ApiModelProperty("经停机场")
    private String stopAirport;

    @ApiModelProperty("经停点出发时刻")
    private String stopDepTime;

    @ApiModelProperty("经停点所在时区")
    private String stopDepLocaltimezone;

    @ApiModelProperty("经停点北京出发时间")
    private String stopDepChinatime;

    @ApiModelProperty("经停点航站楼")
    private String stopAirportTerminal;

    @ApiModelProperty("公务舱级别代码")
    private String firstClass;

    @ApiModelProperty("公务舱数量")
    private Integer firstClassVal;

    @ApiModelProperty("经济舱级别代码")
    private String touristClass;

    @ApiModelProperty("经济舱数量")
    private Integer touristClassVal;

    @ApiModelProperty("国内国际标志")
    private String interFlag;

    @ApiModelProperty("最近更新时间")
    private LocalDateTime updatetime;

    @ApiModelProperty("最近更新时间戳")
    private Long timestamp;

    @ApiModelProperty("机型")
    private String planType;

    private String meal;

    @ApiModelProperty("餐食代码")
    private String mealCode;

    @ApiModelProperty("记录插入时间")
    private LocalDateTime createtime;

    @ApiModelProperty("来源")
    private String dataSource;
}
