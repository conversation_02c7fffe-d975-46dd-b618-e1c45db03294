package com.juneyaoair.ecs.manage.dto.activity.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActivityCouponDetail {
    private String id;
    @NotNull(message = "活动礼包不能为空")
    private String couponGiftId;
    @NotNull(message = "优惠券码不能为空")
    private String couponId;
    private Integer couponPrice;
    @NotNull(message = "优惠券名称不能为空")
    private String couponName;
    private String couponActivityName;
    @NotNull(message = "使用规则不能为空")
    private String couponUseRule;
    private String remark;
    private String createUser;
    private String createDate;
    private String lastUpdateUser;
    private String lastUpdateDate;
    private String couponUseStartDate;
    private String couponUseEndDate;
    private String couponGiftName;
    @NotNull(message = "优惠券类型不能为空")
    private String couponType;
    @NotNull(message = "警戒值不能为空")
    private String restNum;
    @NotNull(message = "内部通知不能为空")
    private String callInner;
    //0:优惠券数量不足 1:优惠券数量充足
    private String couponEnough;
    private String isValid;
    private String couponGiftSalePrice;
    private String couponGiftBasePrice;
    private String detailExchangeCode;

}
