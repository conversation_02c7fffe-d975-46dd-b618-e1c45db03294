package com.juneyaoair.ecs.manage.dto.citymanage;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CityLabelInfo {
    //标签名称
   public String labelName;
    //标签跳转地址
   public String labelUrl;
    //标签图片地址,展示图片
   public String labelIconUrl;
    //标签地址顺序
   public int labelOrder;
}