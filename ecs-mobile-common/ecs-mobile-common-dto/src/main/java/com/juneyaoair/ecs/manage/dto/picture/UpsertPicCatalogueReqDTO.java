package com.juneyaoair.ecs.manage.dto.picture;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel
public class UpsertPicCatalogueReqDTO {


    @ApiModelProperty(hidden = false)
    public Long id;
    /**
     * 类型 0：root根; 1：index目录; 2：node节点
     */
    @ApiModelProperty("类型 0：root根; 1：index目录; 2：node节点")
    public Integer type;

    /**
     * node节点图片location
     */
    @ApiModelProperty("node节点图片location")
    public String picLocation;

    /**
     * 父级层级
     */
    @ApiModelProperty("父级层级")
    public Long parentId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    public String name;

    /**
     * 是否删除 0：未删除；1：已删除
     */
    @ApiModelProperty("是否删除 0：未删除；1：已删除")
    public String deletedFlag;

    /**
     * createUser
     */
    @ApiModelProperty("createUser")
    public String createUser;

    /**
     * updateTime
     */
    @ApiModelProperty("updateTime")
    public Date updateTime;

    /**
     * updateUser
     */
    @ApiModelProperty("updateUser")
    public String updateUser;

    /**
     * createTime
     */
    @ApiModelProperty("createTime")
    public Date createTime;
}
