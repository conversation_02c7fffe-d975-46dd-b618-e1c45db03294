package com.juneyaoair.ecs.manage.dto.activity.redeem.signact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignRedeemRecordBO {

    @ApiModelProperty(value = "兑换时间", allowableValues = "", notes = "", example = "")
    private Date redeemTime;

    @ApiModelProperty(value = "奖品名称", allowableValues = "", notes = "", example = "")
    private String awardName;

    @ApiModelProperty(value = "奖品描述", allowableValues = "", notes = "", example = "")
    private String awardDes;

    @ApiModelProperty(value = "会员卡号", allowableValues = "", notes = "", example = "")
    private String ffpNo;

    @ApiModelProperty(value = "消耗货币", allowableValues = "", notes = "", example = "")
    private String pointCount;

    @ApiModelProperty(value = "兑换状态", allowableValues = "", notes = "", example = "")
    private String redeemState;
}
