package com.juneyaoair.ecs.manage.dto.activity.request.osaka;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName OsakaCouponClaimQueryRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/6/23 13:52
 * @Version 1.0
 */
public class OsakaCouponClaimQueryRequest {
    /**
     * <AUTHOR>
     * @Description 商户号
     * @Date 13:07 2025/3/17
     **/
    @NotEmpty(message = "商户号不可为空")
    private String merchantCode;

    /**
     * <AUTHOR>
     * @Description 领取开始时间
     * @Date 13:07 2025/3/17
     **/
    @NotEmpty(message = "券领取开始时间不可为空")
    private String claimStartTime;

    /**
     * <AUTHOR>
     * @Description 领取结束时间
     * @Date 13:07 2025/3/17
     **/
    @NotEmpty(message = "券领取结束时间不可为空")
    private String claimEndTime;

    /**
     * <AUTHOR>
     * @Description 会员卡号
     * @Date 13:07 2025/3/17
     **/
    private String ffpCardNo;


    /**
     * <AUTHOR>
     * @Description 手机号
     * @Date 13:56 2025/6/23
     **/
    private String phoneNumber;


    /**
     * <AUTHOR>
     * @Description 券码
     * @Date 13:56 2025/6/23
     **/
    private String couponCode;

    /**
     * <AUTHOR>
     * @Description 券状态
     * @Date 13:56 2025/6/23
     **/
    private String couponStatus;

    /**
     * <AUTHOR>
     * @Description 票号
     * @Date 13:56 2025/6/23
     **/
    private String ticketNo;

    /**
     * <AUTHOR>
     * @Description 航班号
     * @Date 13:56 2025/6/23
     **/
    private String flightNo;

    /**
     * <AUTHOR>
     * @Description 推荐人
     * @Date 12:58 2025/7/28
     **/
    private String recordSource;

    public OsakaCouponClaimQueryRequest() {
    }

    public OsakaCouponClaimQueryRequest(String merchantCode, String claimStartTime, String claimEndTime, String ffpCardNo, String phoneNumber, String couponCode, String couponStatus, String ticketNo, String flightNo, String recordSource) {
        this.merchantCode = merchantCode;
        this.claimStartTime = claimStartTime;
        this.claimEndTime = claimEndTime;
        this.ffpCardNo = ffpCardNo;
        this.phoneNumber = phoneNumber;
        this.couponCode = couponCode;
        this.couponStatus = couponStatus;
        this.ticketNo = ticketNo;
        this.flightNo = flightNo;
        this.recordSource = recordSource;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getClaimStartTime() {
        return claimStartTime;
    }

    public void setClaimStartTime(String claimStartTime) {
        this.claimStartTime = claimStartTime;
    }

    public String getClaimEndTime() {
        return claimEndTime;
    }

    public void setClaimEndTime(String claimEndTime) {
        this.claimEndTime = claimEndTime;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(String couponStatus) {
        this.couponStatus = couponStatus;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getRecordSource() {
        return recordSource;
    }

    public void setRecordSource(String recordSource) {
        this.recordSource = recordSource;
    }
}
