package com.juneyaoair.ecs.manage.dto.discount.ro;


import lombok.Data;

import java.util.List;

@Data
public class DiscountsRouteActivityRO {



    private String id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 主标题
     */
    private String mainTitle;

    /**
     * 副标题
     */
    private String viceTitle;

    /**
     * 活动参数
     */
    private String activityParam;

    /**
     * 活动链接
     */
    private String activityUrl;

    /**
     * 头图
     */
    private String headPicture;

    /**
     * 活动开始时间
     */
    private String startDate;

    /**
     * 活动结束时间
     */
    private String endDate;


    /**
     * 分享图标
     */
    private String shareUrl;

    /**
     * 数据组
     */
    private List<TwoTitleDataRo> twoTitleDataRoList;
}
