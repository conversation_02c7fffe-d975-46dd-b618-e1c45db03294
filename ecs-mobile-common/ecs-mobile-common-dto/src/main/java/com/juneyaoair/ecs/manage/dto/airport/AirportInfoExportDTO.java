package com.juneyaoair.ecs.manage.dto.airport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel
@Data
public class AirportInfoExportDTO {

    /**
     *
     */
    @ApiModelProperty("")
    public String airportCode;

    /**
     *
     */
    @ApiModelProperty("")
    public String airportName;

    /**
     *
     */
    @ApiModelProperty("")
    public String airportEName;

    /**
     *
     */
    @ApiModelProperty("")
    public String cityCode;

    /**
     *
     */
    @ApiModelProperty("")
    public String nameAbb;

    /**
     *
     */
    @ApiModelProperty("")
    public String englishNameAbb;

    /**
     *
     */
    @ApiModelProperty("")
    public String pinyinAbb;

    /**
     *
     */
    @ApiModelProperty("")
    public Date createDatetime;

    /**
     *
     */
    @ApiModelProperty("")
    public Long createId;

    /**
     *
     */
    @ApiModelProperty("")
    public String baiduMapPoint;

    /**
     *
     */
    @ApiModelProperty("")
    public String airportPinyin;

    /**
     * 网站
     */
    @ApiModelProperty("网站")
    public String website;

    /**
     * 航站楼（国内）
     */
    @ApiModelProperty("航站楼（国内）")
    public String terminalposition;

    /**
     * 值机柜台（国内）
     */
    @ApiModelProperty("值机柜台（国内）")
    public String checkincounter;

    /**
     * 头等舱值机柜台（国内）
     */
    @ApiModelProperty("头等舱值机柜台（国内）")
    public String firstclasscheckincounter;

    /**
     * 售票柜台（国内）
     */
    @ApiModelProperty("售票柜台（国内）")
    public String ticketcounter;

    /**
     * ///值机开始时间
     */
    @ApiModelProperty("")
    public String checkinbegintime;

    /**
     * //值机结束时间
     */
    @ApiModelProperty("")
    public String checkinendtime;

    /**
     * 贵宾休息室（国内）
     */
    @ApiModelProperty("贵宾休息室（国内）")
    public String viproom;

    /**
     * 航站楼（国际）
     */
    @ApiModelProperty("航站楼（国际）")
    public String iTerminalposition;

    /**
     * 值机柜台（国际）
     */
    @ApiModelProperty("值机柜台（国际）")
    public String iCheckincounter;

    /**
     * 头等舱值机柜台（国际）
     */
    @ApiModelProperty("头等舱值机柜台（国际）")
    public String iFirstclasscheckincounter;

    /**
     * 贵宾休息室（国际）
     */
    @ApiModelProperty("贵宾休息室（国际）")
    public String iViproom;

    /**
     * 售票柜台（国际）
     */
    @ApiModelProperty("售票柜台（国际）")
    public String iTicketcounter;

    /**
     * 机场韩文名
     */
    @ApiModelProperty("机场韩文名")
    public String airportKoName;

    /**
     * 机场日文名
     */
    @ApiModelProperty("机场日文名")
    public String airportJpName;

    /**
     * 机场泰文名
     */
    @ApiModelProperty("机场泰文名")
    public String airportThName;

    /**
     * 机场繁体中文名
     */
    @ApiModelProperty("机场繁体中文名")
    public String airportTcName;

    /**
     * 机场量级
     */
    @ApiModelProperty("Small Middle  Large")
    public String zonesLevel; //Small Middle  Large
    /**
     * 登机口关闭时间
     */
    @ApiModelProperty("登机口关闭时间")
    public String gateCloseDate;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    public String status;
    //经度
    @ApiModelProperty("经度")
    public String longitude;
    //纬度
    @ApiModelProperty("纬度")
    public String latitude;
}
