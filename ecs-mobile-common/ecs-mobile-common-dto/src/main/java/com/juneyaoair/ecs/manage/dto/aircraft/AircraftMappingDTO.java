package com.juneyaoair.ecs.manage.dto.aircraft;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> <EMAIL>
 * @version v1.0
 * @date 2023/8/22 14:28
 * @description
 **/
@ApiModel
@Data
public class AircraftMappingDTO {
    @ApiModelProperty("主键ID")
    private String aircraftMappingId;

    @ApiModelProperty("机型ID")
    private String aircraftTypeId;

    @ApiModelProperty("机型编码")
    private String aircraftCode;

    @ApiModelProperty(value = "状态 Y启用 N禁用")
    public String status;

    @ApiModelProperty("创建人")
    public String createUser;

    @ApiModelProperty("创建时间")
    public Date createtime;

    @ApiModelProperty("更新人")
    public String updateUser;

    @ApiModelProperty("更新时间")
    public Date updatetime;
}
