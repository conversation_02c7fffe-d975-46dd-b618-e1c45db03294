package com.juneyaoair.ecs.manage.dto.activity.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActivityBaseInfo {
    private String id;
    @NotEmpty(message="活动名称不能为空")
    private String area;
    @NotEmpty(message="开始时间不能为空")
    private String jobStartDate;
    @NotEmpty(message="结束时间不能为空")
    private String jobEndDate;
    @NotEmpty(message = "活动参数不能为空")
    private String activityParam;
    private String createMan;
    private String createTime;
    private String updateMan;
    private String updateTime;
    private String nowTime;
    private String shareUrl;
    private String shareTitle;
    private String shareDesc;
    private String shareIconUrl;
    private String activityRules;
    private String appHeadPicture;
    private String pcHeadPicture;
    private String bgColorCode;
    private String btnColorCode;
    private String seckillStartTime;
    private String seckillEndTime;
    private List<String> dataNames;
}
