package com.juneyaoair.ecs.manage.dto.activity.redeem.segact;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SegRedeemDTO {

    @ApiModelProperty(value = "兑换时间", allowableValues = "", notes = "", example = "")
    private Date redeemTime;

    @ApiModelProperty(value = "奖品名", allowableValues = "", notes = "", example = "")
    private String awardName;

    @ApiModelProperty(value = "奖品描述", allowableValues = "", notes = "", example = "")
    private String awardDes;

    @ApiModelProperty(value = "会员卡号", allowableValues = "", notes = "", example = "")
    private String ffpNo;

    @ApiModelProperty(value = "消耗积分", allowableValues = "", notes = "", example = "")
    private String pointCount;

    @ApiModelProperty(value = "票号", allowableValues = "", notes = "", example = "")
    private String ticketNo;

    @ApiModelProperty(value = "兑换状态", allowableValues = "", notes = "", example = "")
    private String redeemState;

}
