package com.juneyaoair.ecs.seat.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 区域开放时间详情
 * @created 2024/3/20 9:35
 */
@Data
public class SeatBaseOpentime {

    @ApiModelProperty(value = "座位开放时间ID")
    private Long seatOpenTimeId;

    @ApiModelProperty(value = "基础开放规则ID")
    private Long opentimeSetId;

    @ApiModelProperty(value = "座位开放开始时间")
    private Integer startHours;

    @ApiModelProperty(value = "座位开放结束时间")
    private Integer endHours;

    @ApiModelProperty(value = "座位区域ID")
    private Long areaId;

    @ApiModelProperty(value = "座位是否开放")
    private Integer delFlg;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "座位")
    private List<String> seatNos;
}
