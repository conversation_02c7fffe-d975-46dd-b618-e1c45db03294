package com.juneyaoair.ecs.manage.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/14 14:02
 */
public enum OpTypeEnum {
    borrowingMethod("设置航班借位顺序"),
    cabinNest("设置航节主舱嵌套关系"),
    freesaleMCFlightStatus("设置代码共享市场方航节航段舱位状态"),
    groupLimitSale("设置航节主舱团队限制销售数"),
    limitSale("设置航段舱位限制销售数"),
    limitSaleAndOn("设置航段舱位限制销售数和限制销售标识"),
    limitSaleOn("设置航段舱位限制销售标识"),
    maxSeatSold("设置航段舱位最大可售数"),
    minSeatReserved("设置航段舱位最小保留数"),
    nest("设置航段舱位嵌套关系"),
    payload("设置航节主舱超售数"),
    permanentRequest("设置航段舱位永久申请标识"),
    stopBooking("设置航节主舱禁止销售标识"),
    waitingListLimit("设置航段舱位候补数"),
    classInhibit("设置航段舱位禁止销售标识"),
    autoWaitlistClearance("设置航段舱位自动候补证实标识"),
    activation("航班初始化激活"),
    dataMigration("后台数据维护操作引起的变更，前端不展示"),
    scheduleChange("航班计划变更报文处理。包括 NEW、RPL、CNL、EQT、TIM、ADM 等各种类型"),
    legClassLimitSaleAndOn("设置航段舱位自动候补证实标识"),
    emergencyLock("设置航节紧急锁定"),
    capacity("设置航节主舱布局数"),
    avsTable(""),
    OrBkPNR("记录OverBooking旅客PNR编号"),
    depInit("记录离港初始化报文"),
    PFQ("记录PFQ报文内容"),
    OPFQ("记录PFQreopen报文内容"),
    stChange("记录st报文"),
    scsChange(""),
    ;
    private String desc;

    OpTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
