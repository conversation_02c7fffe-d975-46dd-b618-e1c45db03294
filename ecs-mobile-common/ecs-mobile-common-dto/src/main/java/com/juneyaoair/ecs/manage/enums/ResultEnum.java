package com.juneyaoair.ecs.manage.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/8 13:04
 */
public enum ResultEnum {
    S1001("1001","成功"),
    S10001("10001","成功"),

    NET_ERROR("N9999","网络请求异常，请稍候再试！");
    private String resultCode;
    private String resultInfo;

    ResultEnum(String resultCode, String resultInfo) {
        this.resultCode = resultCode;
        this.resultInfo = resultInfo;
    }

    public String getResultCode() {
        return resultCode;
    }

    public String getResultInfo() {
        return resultInfo;
    }
}
