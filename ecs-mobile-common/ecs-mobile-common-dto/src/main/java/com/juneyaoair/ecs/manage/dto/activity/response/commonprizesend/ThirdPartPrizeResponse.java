package com.juneyaoair.ecs.manage.dto.activity.response.commonprizesend;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * @ClassName ThirdCouponResponse
 * @Description
 * <AUTHOR>
 * @Date 2025/4/1 21:41
 * @Version 1.0
 */
public class ThirdPartPrizeResponse {

    /**
     * <AUTHOR>
     * @Description 活动号
     * @Date 21:44 2025/4/1
     **/
    @ExcelProperty(value = "活动号", index = 0)
    private String activityCode;

    /**
     * <AUTHOR>
     * @Description 商户号
     * @Date 21:44 2025/4/1
     **/
    @ExcelProperty(value = "商户号", index = 1)
    private String merchantCode;

    /**
     * <AUTHOR>
     * @Description 奖品编码
     * @Date 21:44 2025/4/1
     **/
    @ExcelProperty(value = "奖品编码", index = 2)
    private String prizeCode;

    @ExcelProperty(value = "奖品名称", index = 3)
    private String prizeName;

    @ExcelProperty(value = "奖品类型", index = 4)
    private String prizeType;

    @ExcelProperty(value = "奖品数量", index = 5)
    private Integer prizeAmount;

    /**
     * <AUTHOR>
     * @Description 奖品发送状态
     * @Date 21:44 2025/4/1
     **/
    @ExcelProperty(value = "奖品领取状态", index = 6)
    private String prizeStatus;

    /**
     * <AUTHOR>
     * @Description 使用账户
     * @Date 21:44 2025/4/1
     **/
    @ExcelProperty(value = "领取者卡号", index = 7)
    private String ffpCardNo;

    /**
     * <AUTHOR>
     * @Description 使用时间
     * @Date 21:44 2025/4/1
     **/
    @ExcelProperty(value = "领取时间", index = 8)
    private String receiveTime;

    public ThirdPartPrizeResponse() {
    }

    public ThirdPartPrizeResponse(String activityCode, String merchantCode, String prizeCode, String prizeName, String prizeType, Integer prizeAmount, String prizeStatus, String ffpCardNo, String receiveTime) {
        this.activityCode = activityCode;
        this.merchantCode = merchantCode;
        this.prizeCode = prizeCode;
        this.prizeName = prizeName;
        this.prizeType = prizeType;
        this.prizeAmount = prizeAmount;
        this.prizeStatus = prizeStatus;
        this.ffpCardNo = ffpCardNo;
        this.receiveTime = receiveTime;
    }

    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getPrizeCode() {
        return prizeCode;
    }

    public void setPrizeCode(String prizeCode) {
        this.prizeCode = prizeCode;
    }

    public String getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(String prizeType) {
        this.prizeType = prizeType;
    }

    public Integer getPrizeAmount() {
        return prizeAmount;
    }

    public void setPrizeAmount(Integer prizeAmount) {
        this.prizeAmount = prizeAmount;
    }

    public String getPrizeStatus() {
        return prizeStatus;
    }

    public void setPrizeStatus(String prizeStatus) {
        this.prizeStatus = prizeStatus;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(String receiveTime) {
        this.receiveTime = receiveTime;
    }
}
