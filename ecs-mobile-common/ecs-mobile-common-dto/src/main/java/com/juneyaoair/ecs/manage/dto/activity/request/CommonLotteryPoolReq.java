package com.juneyaoair.ecs.manage.dto.activity.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonLotteryPoolReq
 * @Description
 * @createTime 2023年05月17日 14:49
 */
public class CommonLotteryPoolReq {

    @ApiModelProperty("记录唯一编号")
    private String id;

    /**
     * 奖池编码
     */
    @ApiModelProperty("奖池编码")
    private String prizePoolCode;

    /**
     * 奖池名称
     */
    @ApiModelProperty("奖池名称")
    private String prizePoolName;

    public CommonLotteryPoolReq() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPrizePoolCode() {
        return prizePoolCode;
    }

    public void setPrizePoolCode(String prizePoolCode) {
        this.prizePoolCode = prizePoolCode;
    }

    public String getPrizePoolName() {
        return prizePoolName;
    }

    public void setPrizePoolName(String prizePoolName) {
        this.prizePoolName = prizePoolName;
    }
}
