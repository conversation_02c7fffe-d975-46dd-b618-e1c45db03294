package com.juneyaoair.ecs.manage.dto.specialAirline;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025-01-26
 */
@Data
public class SpecialAirlineBase {

    @NotBlank(message = "数据ID不能为空")
    @ApiModelProperty(value = "数据ID", required = true)
    private String airlineId;

    @ApiModelProperty(value = "操作人", hidden = true)
    private String operator;

}
