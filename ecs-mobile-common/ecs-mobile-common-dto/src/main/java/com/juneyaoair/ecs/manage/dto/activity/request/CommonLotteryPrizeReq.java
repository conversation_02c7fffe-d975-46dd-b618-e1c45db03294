package com.juneyaoair.ecs.manage.dto.activity.request;

import java.util.List;

public class CommonLotteryPrizeReq {

    private String id;
    /**
     * 奖池编码
     */
    private String prizePoolCode;

    /**
     * 奖品编码
     */
    private String prizeCode;

    /**
     * 奖品名称
     */
    private String prizeName;

    /**
     * 奖品数量
     */
    private Integer prizeAmount;

    /**
     * 中奖概率
     */
    private Double winningRate;

    /**
     * 奖品ICON图地址
     */
    private String iconUrl;

    /**
     * 奖品的排序值
     */
    private String sort;

    /**
     * 子奖品列表
     */
    private List<SubPrizeReq> subPrizeReqList;

    public CommonLotteryPrizeReq() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPrizePoolCode() {
        return prizePoolCode;
    }

    public void setPrizePoolCode(String prizePoolCode) {
        this.prizePoolCode = prizePoolCode;
    }

    public String getPrizeCode() {
        return prizeCode;
    }

    public void setPrizeCode(String prizeCode) {
        this.prizeCode = prizeCode;
    }

    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    public Integer getPrizeAmount() {
        return prizeAmount;
    }

    public void setPrizeAmount(Integer prizeAmount) {
        this.prizeAmount = prizeAmount;
    }

    public Double getWinningRate() {
        return winningRate;
    }

    public void setWinningRate(Double winningRate) {
        this.winningRate = winningRate;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public List<SubPrizeReq> getSubPrizeReqList() {
        return subPrizeReqList;
    }

    public void setSubPrizeReqList(List<SubPrizeReq> subPrizeReqList) {
        this.subPrizeReqList = subPrizeReqList;
    }

}
