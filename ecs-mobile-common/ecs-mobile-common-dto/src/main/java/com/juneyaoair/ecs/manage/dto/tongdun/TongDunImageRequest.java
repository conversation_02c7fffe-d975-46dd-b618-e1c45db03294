package com.juneyaoair.ecs.manage.dto.tongdun;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022/8/12
 */
public class TongDunImageRequest {
    /**
     * 图像上传类型
     * data: 图像文件数据url: 图像url链接
     *
     */
    @NotNull(message = "图像上传类型 不能为空!")
    private  String uploadType;

    /**
     *图像文件
     */
    private String imageData;
    /**
     *文件后缀
     */
    private String imgSuffix;
    /**
     * 图像URL
     */
    private  String imageUrl;

    /**
     * 场景
     * porn: 色情场景
     * politics: 政治场景
     * violent: 暴恐场景
     * ad: 图文广告场景
     * logo: LOGO场景
     * tort: 侵权场景
     * vulgarity: 低俗场景
     * teenage：未成年人场景
     */
    @NotNull(message = "场景不能为空!")
    private  String scene;

    /**
     * 设备ID
     */
    private  String deviceId;

    /**
     * 账号
     */
    private  String accountLogin;

    /**
     * 手机号
     */
    private  String accountMobile;

    /**
     * ip地址
     */
    private  String ipAddress;

    public String getUploadType() {
        return uploadType;
    }

    public void setUploadType(String uploadType) {
        this.uploadType = uploadType;
    }

    public String getImageData() {
        return imageData;
    }

    public void setImageData(String imageData) {
        this.imageData = imageData;
    }

    public String getImgSuffix() {
        return imgSuffix;
    }

    public void setImgSuffix(String imgSuffix) {
        this.imgSuffix = imgSuffix;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getAccountLogin() {
        return accountLogin;
    }

    public void setAccountLogin(String accountLogin) {
        this.accountLogin = accountLogin;
    }

    public String getAccountMobile() {
        return accountMobile;
    }

    public void setAccountMobile(String accountMobile) {
        this.accountMobile = accountMobile;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
