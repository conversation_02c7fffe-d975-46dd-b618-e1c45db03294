package com.juneyaoair.ecs.manage.dto.airline.routelabel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListRouteLabelRequestDTO {

    @ApiModelProperty(value = "标签名称", example = "中转住宿")
    private String labelName;

    @ApiModelProperty(value = "单个起飞机场", example = "BUH")
    private String depAirport;

    @ApiModelProperty(value = "单个到达机场", example = "BUH")
    private String arrAirport;

    @ApiModelProperty(value = "航线日期", example = "yyyy-MM-dd")
    private String routeDate;

    @ApiModelProperty(value = "启用状态;启用/禁用", example = "true")
    private Boolean enableStatus;

}
