package com.juneyaoair.ecs.manage.dto.activity.redeem.signact;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SignActUser {

    @ApiModelProperty(value = "会员卡号", allowableValues = "", notes = "", example = "")
    private String ffpNo;

    @ApiModelProperty(value = "签到数", allowableValues = "", notes = "", example = "")
    private String signCount;

    @ApiModelProperty(value = "参与时间/月度", allowableValues = "", notes = "", example = "")
    private String month;
}
