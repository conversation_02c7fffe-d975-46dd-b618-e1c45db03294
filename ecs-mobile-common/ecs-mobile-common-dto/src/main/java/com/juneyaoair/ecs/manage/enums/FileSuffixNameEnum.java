package com.juneyaoair.ecs.manage.enums;

/**
 * Created by yaocf on 2023/1/12  11:06.
 * 常用文件后缀名枚举
 */
public enum FileSuffixNameEnum {
    DEFAULT("","/noticeInfos"),
    PDF(".pdf","/clause"),
    JPG(".jpg","/image"),
    PNG(".png","/image"),
    HTML(".html","/html"),
    ;
     private String suffix;
     private String directory;

    FileSuffixNameEnum(String suffix, String directory) {
        this.suffix = suffix;
        this.directory = directory;
    }

    public String getSuffix() {
        return suffix;
    }

    public String getDirectory() {
        return directory;
    }

    public static FileSuffixNameEnum fileDirectory(String v){
        for (FileSuffixNameEnum c: FileSuffixNameEnum.values()) {
            if (c.suffix.equals(v)) {
                return c;
            }
        }
        return DEFAULT;
    }
}
