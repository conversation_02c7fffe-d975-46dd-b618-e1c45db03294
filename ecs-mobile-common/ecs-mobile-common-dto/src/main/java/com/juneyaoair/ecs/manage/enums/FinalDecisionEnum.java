package com.juneyaoair.ecs.manage.enums;

/**
 * <AUTHOR>
 * 同盾结果枚举
 * @Date 2022/8/12
 */
public enum FinalDecisionEnum {
    ACCEPT("Accept", "1"),
    REVIEW("Review", "2"),
    REJECT("Reject", "3");
    private String code;
    private String name;

    FinalDecisionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static FinalDecisionEnum checkEnum(String v) {
        for (FinalDecisionEnum c : FinalDecisionEnum.values()) {
            if (c.code.equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

