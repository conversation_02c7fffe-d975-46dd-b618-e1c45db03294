package com.juneyaoair.ecs.manage.dto.activity.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AirLineChild {
    private String linkName;//linkName
    private String startDate;
    private String endDate;
    private String dataName;
    private String dataDesc;
    private List<AirLine> airLine;
}
