package com.juneyaoair.ecs.manage.dto.activity.request.event;

import com.juneyaoair.ecs.manage.dto.base.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Author: caolei
 * @Description: 事件信息查询
 * @Date: 2025/04/11 17:15
 * @Modified by:
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EventInfoQueryParam extends PageBase {

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "事件开始时间 格式：yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "事件结束时间 格式：yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty(value = "事件状态 Y：有效 N:无效 D:删除")
    private String status;

}
