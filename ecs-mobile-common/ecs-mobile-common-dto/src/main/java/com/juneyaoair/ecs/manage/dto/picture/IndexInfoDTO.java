package com.juneyaoair.ecs.manage.dto.picture;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel
public class IndexInfoDTO {

    @ApiModelProperty
    public Long indexId;
    /**
     * 类型 0：root根; 1：index目录; 2：node节点
     */
    @ApiModelProperty("'类型 0：root根; 1：index目录; 2：node节点';")
    public int type;
    /**
     *node节点图片location
     */
    @ApiModelProperty("'node节点图片location'")
    public String pic_location;
    /**
     *父层级id
     */
    @ApiModelProperty("父层级id")
    public Long parent_id;
    /**
     * 名称
     */
    @ApiModelProperty("名称")
    public String name;

    /**
     * 是否删除 0：未删除；1：已删除
     */
    @ApiModelProperty("是否删除 0：未删除；1：已删除")
    public String deletedFlag;

    @ApiModelProperty
    public Date createTime;

    @ApiModelProperty
    public String createUser;

    @ApiModelProperty
    public Date updateTime;

    @ApiModelProperty
    public String updateUser;

    @ApiModelProperty("子目录列表")
    public List<IndexInfoDTO> indexInfoList;
}
