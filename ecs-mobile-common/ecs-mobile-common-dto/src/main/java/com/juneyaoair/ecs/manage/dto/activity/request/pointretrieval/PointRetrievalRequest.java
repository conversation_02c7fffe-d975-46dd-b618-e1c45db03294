package com.juneyaoair.ecs.manage.dto.activity.request.pointretrieval;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName PointRetrievalRequest
 * @Description 积分找回流水
 * <AUTHOR>
 * @Date 2025/3/17 13:06
 * @Version 1.0
 */
public class PointRetrievalRequest {


    private String ffpCardNo;

    /**
     * <AUTHOR>
     * @Description 报名开始时间
     * @Date 13:07 2025/3/17
     **/
    private String signUpStartTime;

    /**
     * <AUTHOR>
     * @Description 报名结束时间
     * @Date 13:07 2025/3/17
     **/
    private String signUpEndTime;

    /**
     * <AUTHOR>
     * @Description 领取开始时间
     * @Date 13:07 2025/3/17
     **/
    private String receiveStartTime;

    /**
     * <AUTHOR>
     * @Description 领取结束时间
     * @Date 13:07 2025/3/17
     **/
    private String receiveEndTime;

    public PointRetrievalRequest() {
    }

    public PointRetrievalRequest(String ffpCardNo, String signUpStartTime, String signUpEndTime, String receiveStartTime, String receiveEndTime) {
        this.ffpCardNo = ffpCardNo;
        this.signUpStartTime = signUpStartTime;
        this.signUpEndTime = signUpEndTime;
        this.receiveStartTime = receiveStartTime;
        this.receiveEndTime = receiveEndTime;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getSignUpStartTime() {
        return signUpStartTime;
    }

    public void setSignUpStartTime(String signUpStartTime) {
        this.signUpStartTime = signUpStartTime;
    }

    public String getSignUpEndTime() {
        return signUpEndTime;
    }

    public void setSignUpEndTime(String signUpEndTime) {
        this.signUpEndTime = signUpEndTime;
    }

    public String getReceiveStartTime() {
        return receiveStartTime;
    }

    public void setReceiveStartTime(String receiveStartTime) {
        this.receiveStartTime = receiveStartTime;
    }

    public String getReceiveEndTime() {
        return receiveEndTime;
    }

    public void setReceiveEndTime(String receiveEndTime) {
        this.receiveEndTime = receiveEndTime;
    }
}
