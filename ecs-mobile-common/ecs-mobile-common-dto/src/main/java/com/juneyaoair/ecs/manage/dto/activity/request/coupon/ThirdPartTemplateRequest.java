package com.juneyaoair.ecs.manage.dto.activity.request.coupon;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

/**
 * @ClassName CouponTemplateRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/3/31 9:40
 * @Version 1.0
 */
public class ThirdPartTemplateRequest {
    /**
     * <AUTHOR>
     * @Description 活动号
     * @Date 9:42 2025/3/31
     **/
    @NotEmpty(message = "活动号不可为空")
    private String activityCode;

    /**
     * <AUTHOR>
     * @Description 商户号
     * @Date 9:42 2025/3/31
     **/
    @NotEmpty(message = "商户号不可为空")
    private String merchantCode;

    /**
     * <AUTHOR>
     * @Description 奖品类型不可为空
     * @Date 9:42 2025/3/31
     **/
    @NotEmpty(message = "奖品名称不可为空")
    private String prizeName;

    /**
     * <AUTHOR>
     * @Description 奖品类型不可为空
     * @Date 9:42 2025/3/31
     **/
    @NotEmpty(message = "奖品类型不可为空")
    private String prizeType;

    /**
     * <AUTHOR>
     * @Description 单个奖品的发放数量
     * @Date 9:42 2025/3/31
     **/
    @Min(value = 1, message = "奖品发放数量必须大于0")
    private int prizeAmount;

    /**
     * <AUTHOR>
     * @Description 需要生成的奖品总数
     * @Date 9:42 2025/3/31
     **/
    @Min(value = 1, message = "奖品总发放数量必须大于0")
    private int couponCount;

    public ThirdPartTemplateRequest() {
    }

    public ThirdPartTemplateRequest(String activityCode, String merchantCode, String prizeName, String prizeType, int prizeAmount, int couponCount) {
        this.activityCode = activityCode;
        this.merchantCode = merchantCode;
        this.prizeName = prizeName;
        this.prizeType = prizeType;
        this.prizeAmount = prizeAmount;
        this.couponCount = couponCount;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    public String getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(String prizeType) {
        this.prizeType = prizeType;
    }

    public int getPrizeAmount() {
        return prizeAmount;
    }

    public void setPrizeAmount(int prizeAmount) {
        this.prizeAmount = prizeAmount;
    }

    public int getCouponCount() {
        return couponCount;
    }

    public void setCouponCount(int couponCount) {
        this.couponCount = couponCount;
    }
}
