package com.juneyaoair.ecs.manage.dto.activity.response;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * Created by lzg on 2016-05-17.
 */
public class AppPicture implements Serializable {
    private String picId;
    private String startTime;
    private String endTime;
    private String picUrl;
    private String title;
    private String winName;//窗口名称
    private String description;
    private byte[] description2;
    private String descriptionPlainTxt;//纯文本
    private String status;
    private String statusCode;
    private String updateMan;
    private String updateTime;
    private String createTime;
    private String createMan;
    private String url;
    private String minVersion;
    private String maxVersion;
    private String titleStyle; //新版头部样式设置
    private Integer orderNum;  //排序字段

    public String getMinVersion() {
        return minVersion;
    }

    public void setMinVersion(String minVersion) {
        this.minVersion = minVersion;
    }

    public String getMaxVersion() {
        return maxVersion;
    }

    public void setMaxVersion(String maxVersion) {
        this.maxVersion = maxVersion;
    }

    //保存時文件的ID
    private String[] fileID;   //console使用
    private String fileIds;
    private List<FileInfo> fileInfos;
    private String isLogin;//判断是否需要登录
    private String modeType; //图片发布模式

    public String getPicId() {
        return picId;
    }

    private String isShared; //是否可分享
    private String shareIconUrl;//缩略图
    private String shareDesc;//分享描述
    private String isGiftPoints;//是否送积分
    private String isGiftCoupons;//是否送优惠券

    private Integer shareScore;//分享送积分数
    private String shareCouponsStr;//分享送的优惠券活动码字符串（用，分割）
    private List<String> shareCouponList;//分享送的优惠券活动码

    private String mobileModel;

    private Integer displayTime;

    private String fileType;

    @Override
    public String toString() {
        return "AppPicture{" +
                "picId='" + picId + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", picUrl='" + picUrl + '\'' +
                ", title='" + title + '\'' +
                ", winName='" + winName + '\'' +
                ", description='" + description + '\'' +
                ", description2=" + Arrays.toString(description2) +
                ", descriptionPlainTxt='" + descriptionPlainTxt + '\'' +
                ", status='" + status + '\'' +
                ", statusCode='" + statusCode + '\'' +
                ", updateMan='" + updateMan + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", createTime='" + createTime + '\'' +
                ", createMan='" + createMan + '\'' +
                ", url='" + url + '\'' +
                ", minVersion='" + minVersion + '\'' +
                ", maxVersion='" + maxVersion + '\'' +
                ", fileID=" + Arrays.toString(fileID) +
                ", fileIds='" + fileIds + '\'' +
                ", fileInfos=" + fileInfos +
                ", isLogin='" + isLogin + '\'' +
                ", modeType='" + modeType + '\'' +
                ", isShared='" + isShared + '\'' +
                ", shareIconUrl='" + shareIconUrl + '\'' +
                ", shareDesc='" + shareDesc + '\'' +
                ", isGiftPoints='" + isGiftPoints + '\'' +
                ", isGiftCoupons='" + isGiftCoupons + '\'' +
                ", shareScore='" + shareScore + '\'' +
                ", shareCouponsStr='" + shareCouponsStr + '\'' +
                ", shareCouponList=" + shareCouponList +
                ", mobileModel='" + mobileModel + '\'' +
                ", displayTime='" + displayTime + '\'' +
                ", fileType='" + fileType + '\'' +
                '}';
    }

    public String getShareCouponsStr() {
        return shareCouponsStr;
    }

    public void setShareCouponsStr(String shareCouponsStr) {
        this.shareCouponsStr = shareCouponsStr;
    }

    public List<String> getShareCouponList() {
        return shareCouponList;
    }

    public void setShareCouponList(List<String> shareCouponList) {
        this.shareCouponList = shareCouponList;
    }

    public String getIsShared() {
        return isShared;
    }

    public void setIsShared(String isShared) {
        this.isShared = isShared;
    }

    public String getShareIconUrl() {
        return shareIconUrl;
    }

    public void setShareIconUrl(String shareIconUrl) {
        this.shareIconUrl = shareIconUrl;
    }

    public String getShareDesc() {
        return shareDesc;
    }

    public void setShareDesc(String shareDesc) {
        this.shareDesc = shareDesc;
    }

    public String getIsGiftPoints() {
        return isGiftPoints;
    }

    public void setIsGiftPoints(String isGiftPoints) {
        this.isGiftPoints = isGiftPoints;
    }

    public String getIsGiftCoupons() {
        return isGiftCoupons;
    }

    public void setIsGiftCoupons(String isGiftCoupons) {
        this.isGiftCoupons = isGiftCoupons;
    }

    public void setPicId(String picId) {
        this.picId = picId;
    }


    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUpdateMan() {
        return updateMan;
    }

    public void setUpdateMan(String updateMan) {
        this.updateMan = updateMan;
    }

    public String getCreateMan() {
        return createMan;
    }

    public void setCreateMan(String createMan) {
        this.createMan = createMan;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String[] getFileID() {
        return fileID;
    }

    public void setFileID(String[] fileID) {
        this.fileID = fileID;
    }

    public String getFileIds() {
        return fileIds;
    }

    public void setFileIds(String fileIds) {
        this.fileIds = fileIds;
    }

    public List<FileInfo> getFileInfos() {
        return fileInfos;
    }

    public void setFileInfos(List<FileInfo> fileInfos) {
        this.fileInfos = fileInfos;
    }


    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public byte[] getDescription2() {
        return description2;
    }

    public void setDescription2(byte[] description2) {
        this.description2 = description2;
    }

    public String getDescriptionPlainTxt() {
        return descriptionPlainTxt;
    }

    public void setDescriptionPlainTxt(String descriptionPlainTxt) {
        this.descriptionPlainTxt = descriptionPlainTxt;
    }

    public String getIsLogin() {
        return isLogin;
    }

    public void setIsLogin(String isLogin) {
        this.isLogin = isLogin;
    }

    public String getModeType() {
        return modeType;
    }

    public void setModeType(String modeType) {
        this.modeType = modeType;
    }

    public String getWinName() {
        return winName;
    }

    public void setWinName(String winName) {
        this.winName = winName;
    }

    public String getMobileModel() {
        return mobileModel;
    }

    public void setMobileModel(String mobileModel) {
        this.mobileModel = mobileModel;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getTitleStyle() {
        return titleStyle;
    }

    public void setTitleStyle(String titleStyle) {
        this.titleStyle = titleStyle;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getShareScore() {
        return shareScore;
    }

    public void setShareScore(Integer shareScore) {
        this.shareScore = shareScore;
    }

    public Integer getDisplayTime() {
        return displayTime;
    }

    public void setDisplayTime(Integer displayTime) {
        this.displayTime = displayTime;
    }
}
