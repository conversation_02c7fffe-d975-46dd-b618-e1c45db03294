package com.juneyaoair.ecs.manage.dto.citymanage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DstWtInfoDTO {
    /**
     *
     */
    public String id;

    /**
     *
     */
    public Date createTime;

    /**
     *
     */
    public String createUser;

    /**
     *
     */
    public Date lastUpdateTime;

    /**
     *
     */
    public String lastUpdateUser;
    @ApiModelProperty("夏令时时间")
    public List<DstDateInfo> dstDateInfos;

    /**
     * 夏令时偏移
     */
    @ApiModelProperty("夏令时偏移")
    public String dstOffset;

    /**
     *
     */
    public String dstStart;

    /**
     *
     */
    public String dstEnd;

    /**
     *
     */
    public String dstOppset;

    /**
     *
     */
    public String wtStart;

    /**
     *
     */
    public String wtEnd;

    /**
     *
     */
    public String wtOppset;
}
