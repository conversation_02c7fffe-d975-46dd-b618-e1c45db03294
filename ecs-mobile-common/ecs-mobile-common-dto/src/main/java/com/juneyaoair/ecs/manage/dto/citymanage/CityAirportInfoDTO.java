package com.juneyaoair.ecs.manage.dto.citymanage;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CityAirportInfoDTO {
    public String cityCode;
    public String cityName;

    @ApiModelProperty("城市名称(语言：值)")
    public Map<String, String> cityNameMap;

    @ApiModelProperty(value = "城市名称简称")
    public String nameAbb;

    public String cityPinYin;
    //城市拼音简拼
    public String cityPinYinAbb;
    @ApiModelProperty(value = "城市关键字查询")
    public String cityNameKeyword;
    public String airportCode;

    @ApiModelProperty(value = "机场中文名")
    public String airportName;

    @ApiModelProperty(value = "机场名称简称")
    public List<String> airportNameAbb;

    @ApiModelProperty("城市名称(语言：值)")
    public Map<String, String> airportNameMap;

    public String airportPinYin;
    //机场拼音简拼
    public String airportPinYinAbb;
    @ApiModelProperty(value = "城市机场类型",notes = "city-城市,airport-机场")
    public String cityOrAirportType;
    @ApiModelProperty(value = "是否存在关联机场")
    public boolean hasAirport;
    @ApiModelProperty(value = "邻近机场,仅关联在城市下")
    public List<String> nearbyAirport;
    //D-国内,I-国际
    public String isInternational;
    //城市,机场标签列表
    public List<CityLabelInfo> cityLabelInfoList;
    //经度
    public String longitude;
    //纬度
    public String latitude;

    public String countryName;
    public String provinceName;
    public String region;

}
