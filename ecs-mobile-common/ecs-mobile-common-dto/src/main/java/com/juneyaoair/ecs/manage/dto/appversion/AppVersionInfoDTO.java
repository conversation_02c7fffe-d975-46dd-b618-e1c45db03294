package com.juneyaoair.ecs.manage.dto.appversion;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/15 15:43
 */
@Data
public class AppVersionInfoDTO {
    /**
     * 内置ID
     */
    private String innerId;

    /**
     * 设备（IOS，Android）
     */
    private String deviceType;

    /**
     * 版本号
     */
    private String appVersionNo;

    /**
     * 状态（N：未更新；Y:已更新）
     */
    private String status;

    /**
     * 发布时间
     */
    private Date createTime;

    /**
     * 更新说明
     */
    private String updateDetails;

    /**
     * 强制更新（N:未强制更新；Y：已强制更新）
     */
    private String forceUpdate;

    /**
     * Y为需要登录
     */
    private String loginFlag;

    /**
     * YYYYMMDDhhmmss
     */
    private String versionTimestamp;

    /**
     * Y为支持第三方登录
     */
    private String thirdPartyLoginFlag;

    private int innerIdSort;
}
