package com.juneyaoair.ecs.manage.dto.social.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SocialReportResponse
 * @Description 社会责任报告返回体
 * <AUTHOR>
 * @Date 2024/6/12 16:16
 * @Version 1.0
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SocialResponsibilityReport {

    private Integer id;

    @ApiModelProperty(value = "渠道")
    private String channelCode;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "PDF地址")
    private String srcUrl;

    @ApiModelProperty(value = "排序值")
    private Integer picSequence;

    @ApiModelProperty(value = "备注")
    private String remark;
}
