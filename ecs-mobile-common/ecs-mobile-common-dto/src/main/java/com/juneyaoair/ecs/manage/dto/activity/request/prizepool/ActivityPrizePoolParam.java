package com.juneyaoair.ecs.manage.dto.activity.request.prizepool;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: caolei
 * @Description: 创建活动奖池信息表（适用于奖池中奖品随机发放）参数
 * @Date: 2024/07/09 9:15
 * @Modified by:
 */
@Data
public class ActivityPrizePoolParam {

    @ApiModelProperty(value = "奖池编码")
    private String prizePoolCode;

    @ApiModelProperty(value = "奖池名称")
    private String prizePoolName;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "操作人", hidden = true)
    private String operator;

}
