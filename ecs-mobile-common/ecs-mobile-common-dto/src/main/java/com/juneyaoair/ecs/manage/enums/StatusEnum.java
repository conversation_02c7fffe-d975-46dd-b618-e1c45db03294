package com.juneyaoair.ecs.manage.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/25 17:00
 */
public enum StatusEnum {
    ALL("","全部"),
    ENABLE("1","启用"),
    DISABLED("0","禁用"),
    ;
    private  String code;
    private  String desc;

    StatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
