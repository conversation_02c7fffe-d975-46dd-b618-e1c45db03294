package com.juneyaoair.ecs.manage.dto.activity.redeem.signact;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

@Data
public class SignActAwardDTO {

    @NotNull
    @ApiModelProperty(value = "奖品名称", allowableValues = "", notes = "", example = "")
    private String awardName;

    @NotNull
    @ApiModelProperty(value = "奖品描述", allowableValues = "", notes = "", example = "")
    private String awardDesc;

    @NotNull
    @ApiModelProperty(value = "每月限制", allowableValues = "", notes = "", example = "")
    private String monthlyLimit;

    @NotNull
    @Positive
    @ApiModelProperty(value = "所需积分", allowableValues = "", notes = "", example = "")
    private Integer needPoint;

    @NotNull
    @ApiModelProperty(value = "奖池编码", allowableValues = "", notes = "", example = "")
    private String prizePoolCode;

}
