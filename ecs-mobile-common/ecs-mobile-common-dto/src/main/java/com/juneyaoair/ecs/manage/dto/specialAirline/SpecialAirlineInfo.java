package com.juneyaoair.ecs.manage.dto.specialAirline;

import com.juneyaoair.ecs.manage.dto.common.PicInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-01-26
 */
@Data
public class SpecialAirlineInfo {

    @ApiModelProperty(value = "主键ID")
    private String airlineId;

    @ApiModelProperty(value = "出发城市三字码")
    private String depCityCode;

    @ApiModelProperty(value = "出发城市名称")
    private String depCityName;

    @ApiModelProperty(value = "到达城市三字码")
    private String arrCityCode;

    @ApiModelProperty(value = "到达城市名称")
    private String arrCityName;

    @ApiModelProperty(value = "生效日期",notes = "格式:yyyy-MM-dd")
    private String startTime;

    @ApiModelProperty(value = "失效日期",notes = "格式:yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty(value = "序号")
    private Integer sort;

    @ApiModelProperty(value = "图片信息清单")
    private List<PicInfo> picInfoList;

    @ApiModelProperty(value = "渠道清单")
    private Set<String> channelSet;

}
