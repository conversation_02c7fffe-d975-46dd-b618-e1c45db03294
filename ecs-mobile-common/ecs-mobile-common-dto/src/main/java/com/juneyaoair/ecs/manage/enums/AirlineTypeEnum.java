package com.juneyaoair.ecs.manage.enums;

import java.util.HashMap;
import java.util.Map;

public enum AirlineTypeEnum {
    DOMESTIC("D", "国内"),
    INTERNATIONAL("I", "国际"),
    NONE("none","code error");
    public final String code;
    public final String desc;

    AirlineTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AirlineTypeEnum getEnumByCode(String code) {
        return s_map.getOrDefault(code, NONE);
    }

    private static Map<String, AirlineTypeEnum> s_map = new HashMap<>();

    static {
        for (AirlineTypeEnum value : AirlineTypeEnum.values()) {
            s_map.put(value.code, value);
        }
    }

}
