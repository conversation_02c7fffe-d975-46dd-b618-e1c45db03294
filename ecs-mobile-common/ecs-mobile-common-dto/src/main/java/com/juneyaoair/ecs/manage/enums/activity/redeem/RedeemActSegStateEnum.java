package com.juneyaoair.ecs.manage.enums.activity.redeem;

import lombok.Getter;

@Getter
public enum RedeemActSegStateEnum {

    UNABLE("UNABLE", "不符合"),
    NEW("NEW", "新增"),
    COMPUTED("COMPUTED", "已统计"),
    EXPIRED("EXPIRED", "已失效"),
    REDEEMED("REDEEMED", "已兑换");


    /**
     * 业务模块
     */
    private String actSegState;
    /**
     * 业务模块描述
     */
    private String desc;

    RedeemActSegStateEnum(String actSegState, String desc) {
        this.actSegState = actSegState;
        this.desc = desc;
    }


}
