package com.juneyaoair.ecs.manage.dto.airline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;

import javax.validation.constraints.AssertTrue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ApiModel
@Data
public class AirLineLabelReqDTO {
    @ApiModelProperty()
    public String id;

    @ApiModelProperty()
    public String airlineId;

    @ApiModelProperty()
    public String sequence;

    @ApiModelProperty()
    public String labelType;

    private String labelImg;

    @ApiModelProperty()
    public String labelName;

    @ApiModelProperty()
    public String url;

    /**
     * 标签适用航班号，英文逗号分割
     */
    @ApiModelProperty("标签适用航班号，英文逗号分割")
    public String flightNos;

    /**
     * 标签生效开始时间
     */
    @ApiModelProperty("标签生效开始时间")
    public String startTime;

    /**
     * 标签生效结束时间
     */
    @ApiModelProperty("标签生效结束时间")
    public String endTime;

    /**
     * 适用航班日期对 List
     */
    @ApiModelProperty("适用航班日期对 List")
    private List<FitFlightDateDTO> fitFlightDateList;

    /**
     * 适用机型
     */
    @ApiModelProperty("适用机型")
    public String applicablModels;

    /**
     * 标签适用浮动显示日期范围，X/Y，前/后
     */
    @ApiModelProperty("标签适用浮动显示日期范围，X/Y，前/后")
    public String floatingDate;


    @AssertTrue(message = "浮动显示日期格式不正确")
    public boolean isFloatingDateEnable() {
        if (Strings.isNotBlank(floatingDate)) {
            List<String> list = new ArrayList<>(Arrays.asList(floatingDate.split("/")));
            for (String s : list) {
                if (!NumberUtils.isDigits(s)) {
                    return false;
                }
            }
        }
        return true;
    }
}
