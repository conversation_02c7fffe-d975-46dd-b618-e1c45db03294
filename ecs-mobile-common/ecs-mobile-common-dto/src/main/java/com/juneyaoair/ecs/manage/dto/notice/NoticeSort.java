package com.juneyaoair.ecs.manage.dto.notice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeSort {
    /**
     * <AUTHOR>
     * @Description 主键id
     * @Date 10:06 2023/12/21
     **/
    private String ntId;

    /**
     * <AUTHOR>
     * @Description 字典id
     * @Date 10:06 2023/12/21
     **/
    private String dictId;

    /**
     * <AUTHOR>
     * @Description 分类名称
     * @Date 10:06 2023/12/21
     **/
    private String ntName;

    /**
     * <AUTHOR>
     * @Description 显示序号
     * @Date 10:06 2023/12/21
     **/
    private String ntCode;

    /**
     * <AUTHOR>
     * @Description 备注
     * @Date 10:06 2023/12/21
     **/
    private String ntDescription;

    /**
     * <AUTHOR>
     * @Description 显示标记
     * @Date 10:06 2023/12/21
     **/
    private String canShow;

    private List<NoticeInfo> noticeList;
    private DICTValue dictValue;
    private String diId;
    private String diName;


}
