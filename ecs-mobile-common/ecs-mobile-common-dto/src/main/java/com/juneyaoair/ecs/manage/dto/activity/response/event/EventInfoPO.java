package com.juneyaoair.ecs.manage.dto.activity.response.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.juneyaoair.ecs.manage.dto.activity.common.DbBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2025-04-11 14:42
 * @description： 事件信息表
 * @modified By：
 * @version: $
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Table(name = "T_EVENT_INFO")
public class EventInfoPO extends DbBase {

    @Id
    @ApiModelProperty(value = "事件ID")
    private String eventInfoId;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "事件开始时间")
    private Date startTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "事件结束时间")
    private Date endTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "领取开始时间")
    private Date receiveStartTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "领取结束时间")
    private Date receiveEndTime;

    @ApiModelProperty(value = "领取方式 GROUP：基于GROUP_CODE领取 PRIZE：基于PRIZE_CODE领取")
    private String receiveType;

    @ApiModelProperty(value = "事件状态 Y：有效 N:无效 D:删除")
    private String status;

}
