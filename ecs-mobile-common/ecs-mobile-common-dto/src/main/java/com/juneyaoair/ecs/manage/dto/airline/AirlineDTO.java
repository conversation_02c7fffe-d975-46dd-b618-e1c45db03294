package com.juneyaoair.ecs.manage.dto.airline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel
@Data
public class AirlineDTO {
    /**
     *
     */
    @ApiModelProperty("航线Id")
    public String airlineId;

    /**
     *
     */
    @ApiModelProperty("出发机场")
    public String depAirport;

    /**
     *
     */
    @ApiModelProperty("出发城市")
    public String depCity;

    /**
     *
     */
    @ApiModelProperty("到达机场")
    public String arrAirport;

    /**
     *
     */
    @ApiModelProperty("达到城市")
    public String arrCity;

    /**
     *
     */
    @ApiModelProperty("航空公司")
    public String carrierCompany;

    /**
     *
     */
    @ApiModelProperty("是国际航线")
    public String isInternationalAirline;

    /**
     * Y:经停 N：直达 M：联程航线国内 F：联程航线国外
     */
    @ApiModelProperty("Y:经停 N：直达 M：联程航线国内 F：联程航线国外")
    public String isTransit;

    /**
     *
     */
    @ApiModelProperty("中转城市")
    public String transitCity;

    /**
     *
     */
    @ApiModelProperty("中转机场")
    public String transitAirport;

    /**
     *
     */
    @ApiModelProperty("是吉祥航线")
    public String isHoLine;

    /**
     *
     */
    @ApiModelProperty("航线起始日期")
    public Date airlineBegindate;

    /**
     *
     */
    @ApiModelProperty("航线结束日期")
    public Date airlineEnddate;

    /**
     *
     */
    @ApiModelProperty("备注")
    public String airlineFrontRemark;

    @ApiModelProperty("createMan")
    public String createMan;

    /**
     *
     */
    @ApiModelProperty("createTime")
    public Date createTime;

    /**
     *
     */
    @ApiModelProperty("禁用标记")
    public String delflag;

    @ApiModelProperty("是否支持行李直达 Y--支持")
    public String isBaggageDirect; //是否支持行李直达 Y--支持

    /**
     * JOB生成的addon航线标识
     */
    @ApiModelProperty("JOB生成的addon航线标识")
    public String addonRemark;

    @ApiModelProperty("标签列表")
    public List<AirlineLabelDTO> listLabel;
    @ApiModelProperty("createName")
    public String createName;
}

