package com.juneyaoair.ecs.manage.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/16 18:03
 */
public enum YorNEnum {
    Y("Y"),
    N("N");
    private String str;

    YorNEnum(String str) {
        this.str = str;
    }

    public String getStr() {
        return str;
    }

    public static boolean checkEnum(String v){
        for (YorNEnum c: YorNEnum.values()) {
            if (c.str.equals(v)) {
                return true;
            }
        }
        return false;
    }
}
