package com.juneyaoair.ecs.manage.dto.flightdistance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/4/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlightDistancePricedLogsDTO {
    private String id;
    private String flightDistanceId;
    private String changeType;
    private String changeComment;
    private String noticeFlag;
    private String createdUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createdTime;
}
