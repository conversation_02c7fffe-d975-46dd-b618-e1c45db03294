package com.juneyaoair.ecs.manage.dto.activity.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActivityAirLine {
    private String id;
    @NotEmpty(message="礼包名称不能为空")
    private Integer orderNo;
    @NotEmpty(message="开始时间不能为空")
    private String arrCode;
    private String sendCode;
    private String arrCityName;
    private String depCityName;
    private String seckillCabin;
    private Integer seckillPrice;
    private String childInfoId;
    private Integer seqNo;
    private String filghtNumber;

}
