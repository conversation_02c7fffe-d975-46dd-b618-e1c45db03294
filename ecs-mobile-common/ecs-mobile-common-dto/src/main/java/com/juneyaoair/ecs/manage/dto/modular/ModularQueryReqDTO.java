package com.juneyaoair.ecs.manage.dto.modular;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ModularQueryReqDTO", description = "服务配置查询请求")
public class ModularQueryReqDTO {
    
    @ApiModelProperty("模块名称")
    private String name;
    
    @ApiModelProperty("上级模块")
    private String superiorModule;
    
    @ApiModelProperty("渠道")
    private String channelCode;
    
    @ApiModelProperty("模块地址")
    private String url;
} 