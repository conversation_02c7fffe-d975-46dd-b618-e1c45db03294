package com.juneyaoair.ecs.manage.dto.activity.response.pointretrieval;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * @ClassName PointRetrievalResponse
 * @Description 积分找回返回体
 * <AUTHOR>
 * @Date 2025/3/17 13:23
 * @Version 1.0
 */
public class PointRetrievalInformation {

    /**
     * <AUTHOR>
     * @Description 会员卡号
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "会员卡号", index = 0)
    private String ffpCardNo;

    /**
     * <AUTHOR>
     * @Description 报名时间
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "报名时间", index = 1)
    private String signUpTime;

    /**
     * <AUTHOR>
     * @Description 总的飞行次数
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "总飞行次数", index = 2)
    private Integer flightTimes;

    /**
     * <AUTHOR>
     * @Description 总的失效积分
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "总失效积分", index = 3)
    private Integer invalidScores;

    /**
     * <AUTHOR>
     * @Description 单次领取积分数量
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "单次积分领取数量", index = 4)
    private Integer receiveScores;

    /**
     * <AUTHOR>
     * @Description 单次积分领取状态
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "单次积分领取状态", index = 5)
    private String receiveStatus;

    /**
     * <AUTHOR>
     * @Description 单次积分领取时间
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "单次积分领取时间", index = 6)
    private String receiveTime;


    public PointRetrievalInformation() {
    }

    public PointRetrievalInformation(String ffpCardNo, String signUpTime, Integer flightTimes, Integer invalidScores, Integer receiveScores, String receiveTime, String receiveStatus) {
        this.ffpCardNo = ffpCardNo;
        this.signUpTime = signUpTime;
        this.flightTimes = flightTimes;
        this.invalidScores = invalidScores;
        this.receiveScores = receiveScores;
        this.receiveTime = receiveTime;
        this.receiveStatus = receiveStatus;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getSignUpTime() {
        return signUpTime;
    }

    public void setSignUpTime(String signUpTime) {
        this.signUpTime = signUpTime;
    }

    public Integer getFlightTimes() {
        return flightTimes;
    }

    public void setFlightTimes(Integer flightTimes) {
        this.flightTimes = flightTimes;
    }

    public Integer getInvalidScores() {
        return invalidScores;
    }

    public void setInvalidScores(Integer invalidScores) {
        this.invalidScores = invalidScores;
    }

    public Integer getReceiveScores() {
        return receiveScores;
    }

    public void setReceiveScores(Integer receiveScores) {
        this.receiveScores = receiveScores;
    }

    public String getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(String receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getReceiveStatus() {
        return receiveStatus;
    }

    public void setReceiveStatus(String receiveStatus) {
        this.receiveStatus = receiveStatus;
    }
}
