package com.juneyaoair.ecs.manage.dto.activity.response.passport;

import com.alibaba.excel.annotation.ExcelProperty;

import java.util.Date;

/**
 * @ClassName PassportInformation
 * @Description
 * <AUTHOR>
 * @Date 2025/4/2 20:52
 * @Version 1.0
 */
public class  PassportInformation {

    /**
     * <AUTHOR>
     * @Description 会员卡号
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "会员卡号", index = 0)
    private String ffpCardNo;

    /**
     * <AUTHOR>
     * @Description 总飞行次数
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "总飞行次数", index = 1)
    private Integer flightTimes;
    /**
     * <AUTHOR>
     * @Description 单次领取积分数量
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "奖品ID", index = 2)
    private String prizeId;

    /**
     * <AUTHOR>
     * @Description 奖品类型
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "奖品类型", index = 3)
    private String prizeType;

    /**
     * <AUTHOR>
     * @Description 奖品领取数量
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "奖品领取数量", index = 4)
    private Integer prizeAmount;

    /**
     * <AUTHOR>
     * @Description 奖品领取状态
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "奖品领取状态", index = 5)
    private String prizeStatus;

    /**
     * <AUTHOR>
     * @Description 奖品领取时间
     * @Date 13:32 2025/3/17
     **/
    @ExcelProperty(value = "奖品领取时间", index = 6)
    private String receiveTime;

    public PassportInformation() {
    }

    public PassportInformation(String ffpCardNo, Integer flightTimes, String prizeId, String prizeType, Integer prizeAmount, String prizeStatus, String receiveTime) {
        this.ffpCardNo = ffpCardNo;
        this.flightTimes = flightTimes;
        this.prizeId = prizeId;
        this.prizeType = prizeType;
        this.prizeAmount = prizeAmount;
        this.prizeStatus = prizeStatus;
        this.receiveTime = receiveTime;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public Integer getFlightTimes() {
        return flightTimes;
    }

    public void setFlightTimes(Integer flightTimes) {
        this.flightTimes = flightTimes;
    }

    public String getPrizeId() {
        return prizeId;
    }

    public void setPrizeId(String prizeId) {
        this.prizeId = prizeId;
    }

    public String getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(String prizeType) {
        this.prizeType = prizeType;
    }

    public Integer getPrizeAmount() {
        return prizeAmount;
    }

    public void setPrizeAmount(Integer prizeAmount) {
        this.prizeAmount = prizeAmount;
    }

    public String getPrizeStatus() {
        return prizeStatus;
    }

    public void setPrizeStatus(String prizeStatus) {
        this.prizeStatus = prizeStatus;
    }

    public String getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(String receiveTime) {
        this.receiveTime = receiveTime;
    }
}
