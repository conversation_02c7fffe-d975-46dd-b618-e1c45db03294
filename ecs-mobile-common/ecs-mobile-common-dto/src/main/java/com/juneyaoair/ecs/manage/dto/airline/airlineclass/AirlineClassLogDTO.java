package com.juneyaoair.ecs.manage.dto.airline.airlineclass;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class AirlineClassLogDTO {

    @ApiModelProperty(value = "航空公司代码;例HO、MU、9C")
    private String airlineCode;

    @ApiModelProperty(value = "公务舱舱位代码;例 J/C/D/A/R/I")
    private String businessClass;

    @ApiModelProperty(value = "经济舱舱位代码;例 Y/B/M/U/H/Q/V/W/S/T/Z/E/K/G/L/N/X")
    private String economyClass;

    @ApiModelProperty(value = "生效时间")
    private Date effectiveDate;

    @ApiModelProperty(value = "失效时间")
    private Date expirationDate;

    @ApiModelProperty(value = "状态;生效/失效,1/0")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

}
