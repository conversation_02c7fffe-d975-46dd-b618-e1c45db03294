package com.juneyaoair.ecs.manage.dto.citymanage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class CityWarnDTO {
    @ApiModelProperty()
    public String id;
    @ApiModelProperty()
    public String depShowDate;
    @ApiModelProperty()
    public String depWarnTitle;
    @ApiModelProperty()
    public String depWarnContent;
    @ApiModelProperty()
    public String arrShowDate;
    @ApiModelProperty()
    public String arrWarnTitle;
    @ApiModelProperty()
    public String arrWarnContent;
    @ApiModelProperty(example = "dept:出发；arr到达")
    public String arrOrDept;
    /**
     * 出发城市航线类型  I 国际  D 国内
     */
    @ApiModelProperty("出发城市航线类型  I 国际  D 国内")
    public String depRouteType;

    /**
     * 到达城市航线类型  I 国际  D 国内
     */
    @ApiModelProperty("到达城市航线类型  I 国际  D 国内")
    public String arrRouteType;
}
