package com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName IntegratingMarketingRewardResponse
 * @Description
 * <AUTHOR>
 * @Date 2025/6/29 14:33
 * @Version 1.0
 */
@Data
public class IntegratingMarketingRewardResponse {

    /**
     * <AUTHOR>
     * @Description 员工工号
     * @Date 14:34 2025/6/29
     **/
    @ExcelProperty(value = "员工工号", index = 0)
    private String employeeNumber;

    /**
     * <AUTHOR>
     * @Description 员工所属部门
     * @Date 14:34 2025/6/29
     **/
    @ExcelProperty(value = "员工所属部门", index = 1)
    private String department;

    /**
     * <AUTHOR>
     * @Description 员工姓名
     * @Date 14:34 2025/6/29
     **/
    @ExcelProperty(value = "员工姓名", index = 2)
    private String employeeName;

    /**
     * <AUTHOR>
     * @Description 所属月份
     * @Date 14:34 2025/6/29
     **/
    @ExcelProperty(value = "所属月份", index = 3)
    private String monthBelong;

    /**
     * <AUTHOR>
     * @Description 营销总奖励
     * @Date 14:34 2025/6/29
     **/
    @ExcelProperty(value = "营销总奖励", index = 4)
    private Long totalRewards;

}
