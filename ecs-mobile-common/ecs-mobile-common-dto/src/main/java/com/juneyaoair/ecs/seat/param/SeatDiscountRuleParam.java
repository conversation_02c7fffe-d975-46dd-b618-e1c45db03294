package com.juneyaoair.ecs.seat.param;

import com.juneyaoair.ecs.manage.dto.base.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 常规价格折扣查询
 * @created 2024/3/27 14:31
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SeatDiscountRuleParam extends PageBase {

    @ApiModelProperty("规则ID")
    private String seatDiscountRuleId;

    @ApiModelProperty("机型ID")
    private Long flightTypeId;

    @ApiModelProperty("航线类型. 1:国际航线; 2:国内航线; 3:地区航线")
    private Integer flightRouteType;

    @ApiModelProperty("出发城市三字码")
    private String depCityCode;

    @ApiModelProperty("到达城市三字码")
    private String arrCityCode;

    @ApiModelProperty("是否包含基础规则")
    private Boolean includeBase;

    @ApiModelProperty("有效开始时间 格式：yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty("有效结束时间 格式：yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("状态")
    private String status;

}
