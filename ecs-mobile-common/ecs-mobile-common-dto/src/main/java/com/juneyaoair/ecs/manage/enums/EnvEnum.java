package com.juneyaoair.ecs.manage.enums;

import com.ruoyi.common.core.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 环境枚举
 * @date 2019/8/15  15:43.
 */
public enum EnvEnum {
    DEV("dev"),
    TEST("test"),
    PRE("pre"),
    PRO("pro");
    private String env;

    EnvEnum(String env) {
        this.env = env;
    }

    public String getEnv() {
        return env;
    }

    public static EnvEnum getEnumByEnv(String env) {
        if (StringUtils.isEmpty(env)) {
            return null;
        }
        return map.getOrDefault(env.toLowerCase(), null);
    }

    private static Map<String, EnvEnum> map = new HashMap<>();

    static {
        for (EnvEnum value : EnvEnum.values()) {
            map.put(value.getEnv(), value);
        }
    }
}
