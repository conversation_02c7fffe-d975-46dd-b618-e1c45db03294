package com.juneyaoair.ecs.manage.dto.activity.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * @Author: caolei
 * @Description: 数据库基础字段
 * @Date: 2021/12/17 14:32
 * @Modified by:
 */
@Data
public class DbBase {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @Column(name = "CREATE_TIME")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    @Column(name = "CREATE_USER")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    @Column(name = "UPDATE_USER")
    private String updateUser;

    public void initInsert(String operator){
        operator = StringUtils.isBlank(operator) ? "SYSTEM" : operator;
        setCreateUser(operator);
        setUpdateUser(operator);
        Date now = new Date();
        setCreateTime(now);
        setUpdateTime(now);
    }

    public void initUpdate(String operator){
        operator = StringUtils.isBlank(operator) ? "SYSTEM" : operator;
        setUpdateUser(operator);
        setUpdateTime(new Date());
    }

}