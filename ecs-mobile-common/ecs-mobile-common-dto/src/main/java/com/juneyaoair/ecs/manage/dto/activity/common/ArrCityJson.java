package com.juneyaoair.ecs.manage.dto.activity.common;

/**
 * Created by lzg on 2016-05-03.
 */
public class ArrCityJson {
    private String cityCode;
    private String cityName;
    private String cityEName;
    private String cityPinYin;
    private String cityPinYinAbb;
    private String countryCode;
    private String countryNm;
    private String isHotCity;
    private String cityTimeZone;
    private String isInternational;
    private String airportCode;
    private String airportName;
    private String airportEName;

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityEName() {
        return cityEName;
    }

    public void setCityEName(String cityEName) {
        this.cityEName = cityEName;
    }

    public String getCityPinYin() {
        return cityPinYin;
    }

    public void setCityPinYin(String cityPinYin) {
        this.cityPinYin = cityPinYin;
    }

    public String getCityPinYinAbb() {
        return cityPinYinAbb;
    }

    public void setCityPinYinAbb(String cityPinYinAbb) {
        this.cityPinYinAbb = cityPinYinAbb;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountryNm() {
        return countryNm;
    }

    public void setCountryNm(String countryNm) {
        this.countryNm = countryNm;
    }

    public String getIsHotCity() {
        return isHotCity;
    }

    public void setIsHotCity(String isHotCity) {
        this.isHotCity = isHotCity;
    }

    public String getCityTimeZone() {
        return cityTimeZone;
    }

    public void setCityTimeZone(String cityTimeZone) {
        this.cityTimeZone = cityTimeZone;
    }

    public String getIsInternational() {
        return isInternational;
    }

    public void setIsInternational(String isInternational) {
        this.isInternational = isInternational;
    }

    public String getAirportCode() {
        return airportCode;
    }

    public void setAirportCode(String airportCode) {
        this.airportCode = airportCode;
    }

    public String getAirportName() {
        return airportName;
    }

    public void setAirportName(String airportName) {
        this.airportName = airportName;
    }

    public String getAirportEName() {
        return airportEName;
    }

    public void setAirportEName(String airportEName) {
        this.airportEName = airportEName;
    }
}
