package com.juneyaoair.ecs.manage.dto.activity.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActivityCommodity  {
    private String id;
    private String commodityName;
    private Integer currentPrice;
    private Integer price;
    private String productId;
    private String pcPicUrl;
    private String appPicUrl;
    private Integer orderNo;
    private String childInfoId;
    private Integer seqNo;
}
