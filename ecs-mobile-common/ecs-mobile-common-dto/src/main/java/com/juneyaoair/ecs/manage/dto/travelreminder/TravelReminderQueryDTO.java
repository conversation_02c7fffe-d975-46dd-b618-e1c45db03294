package com.juneyaoair.ecs.manage.dto.travelreminder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TravelReminderQueryDTO {
    @ApiModelProperty(value = "提醒主题")
    private String theme;
    
    @ApiModelProperty(value = "渠道")
    private String channel;
    
    @ApiModelProperty(value = "维护时间范围开始")
    private Date maintainStartTime;
    
    @ApiModelProperty(value = "维护时间范围结束")
    private Date maintainEndTime;
    
    @ApiModelProperty(value = "维护人")
    private String maintainer;
} 