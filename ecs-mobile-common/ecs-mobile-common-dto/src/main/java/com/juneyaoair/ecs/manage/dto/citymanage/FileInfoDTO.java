package com.juneyaoair.ecs.manage.dto.citymanage;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@ApiModel
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileInfoDTO {
    /**
     *
     */
    @ApiModelProperty("文件id")
    private String fileid;

    /**
     *
     */
    @ApiModelProperty("linkid")
    private String linkid;

    /**
     *
     */
    @ApiModelProperty("文件名称")
    private String fileName;

    /**
     *
     */
    @ApiModelProperty("")
    private String fileUrl;

    /**
     *
     */
    @ApiModelProperty("")
    private String fileSize;

    /**
     *
     */
    @ApiModelProperty("")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *
     */
    @ApiModelProperty("")
    private String creatorId;

    /**
     *
     */
    @ApiModelProperty("")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     *
     */
    @ApiModelProperty("")
    private String realname;

    /**
     *
     */
    @ApiModelProperty("")
    private String imgserviceurl;

    /**
     *
     */
    @ApiModelProperty("")
    private String hostname;

    /**
     *
     */
    @ApiModelProperty("")
    private String filePath;

    /**
     *
     */
    @ApiModelProperty("")
    private String fileType;
}

