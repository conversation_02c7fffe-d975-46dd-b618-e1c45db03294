package com.juneyaoair.ecs.manage.dto.airline;


import lombok.Data;

import java.util.Date;


@Data
public class AirLineExportDTO {

    /**
     *
     */
    public String airlineId;

    /**
     *
     */
    public String depAirport;

    /**
     *
     */
    public String depCity;

    /**
     *
     */
    public String arrAirport;

    /**
     *
     */
    public String arrCity;

    /**
     *
     */
    public String carrierCompany;

    /**
     *
     */
    public String isInternationalAirline;

    /**
     * Y:经停 N：直达 M：联程航线国内 F：联程航线国外
     */
    public String isTransit;

    /**
     *
     */
    public String transitCity;

    /**
     *
     */
    public String transitAirport;

    /**
     *
     */
    public String isHoLine;

    /**
     *
     */
    public Date airlineBegindate;

    /**
     *
     */
    public Date airlineEnddate;

    /**
     *
     */
    public String airlineFrontRemark;

    /**
     *
     */
    public String createId;

    /**
     *
     */
    public Date createTime;

    /**
     *
     */
    public String delflag;

    public String isBaggageDirect; //是否支持行李直达 Y--支持

    /**
     * JOB生成的addon航线标识
     */
    public String addonRemark;

    public String listLabel;

    public String createName;
}
