package com.juneyaoair.ecs.manage.enums;

import java.util.HashMap;
import java.util.Map;

public enum MessageTypeEnum {

    MSG_CD_NOTICE("MSG_CD_NOTICE", "公告"),
    MSG_CD_WEIXUN("MSG_CD_WEIXUN", "微讯"),
    NONE("NONE", "未知"),
    ;


    private final String code;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private final String desc;

    MessageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MessageTypeEnum getEnumByCode(String code) {
        return s_map.getOrDefault(code, NONE);
    }

    private static Map<String, MessageTypeEnum> s_map = new HashMap<>();

    static {
        for (MessageTypeEnum value : MessageTypeEnum.values()) {
            s_map.put(value.getCode(), value);
        }
    }
}
