package com.juneyaoair.ecs.manage.dto.notice;

/**
 * <AUTHOR>
 * @Description 条款详情
 * @Date 10:21 2023/12/21
 **/

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeMessage{
    /**
     * <AUTHOR>
     * @Description 主键ID
     * @Date 10:21 2023/12/21
     **/
    private String id;

    /**
     * <AUTHOR>
     * @Description 一级模块ID
     * @Date 10:21 2023/12/21
     **/
    private String firstModualId;

    /**
     * <AUTHOR>
     * @Description 二级模块ID
     * @Date 10:21 2023/12/21
     **/
    private String secondModualId;

    /**
     * <AUTHOR>
     * @Description 二级模块ID
     * @Date 10:21 2023/12/21
     **/
    private String oldSecondModualId;

    /**
     * <AUTHOR>
     * @Description 一级模块名称
     * @Date 10:21 2023/12/21
     **/
    private String firstModualName;

    /**
     * <AUTHOR>
     * @Description 二级模块名称
     * @Date 10:21 2023/12/21
     **/
    private String secondModualName;

    /**
     * <AUTHOR>
     * @Description 模块ID
     * @Date 10:21 2023/12/21
     **/
    private String thirdModualId;

    /**
     * <AUTHOR>
     * @Description 模块ID
     * @Date 10:21 2023/12/21
     **/
    private String oldThirdModualId;

    /**
     * <AUTHOR>
     * @Description 序列号
     * @Date 10:21 2023/12/21
     **/
    private String serialNumber;

    /**
     * <AUTHOR>
     * @Description 修改人
     * @Date 10:21 2023/12/21
     **/
    private String person;

    /**
     * <AUTHOR>
     * @Description 修改时间
     * @Date 10:21 2023/12/21
     **/
    private String modifyTime;

    /**
     * <AUTHOR>
     * @Description 是否展示
     * @Date 10:21 2023/12/21
     **/
    private String showAble;

    /**
     * <AUTHOR>
     * @Description 新老数据标识位 Y-新数据
     * @Date 10:21 2023/12/21
     **/
    private String isNew;

    /**
     * <AUTHOR>
     * @Description 模块名称
     * @Date 10:21 2023/12/21
     **/
    private String ntMaintainName;

    /**
     * <AUTHOR>
     * @Description 模块描述
     * @Date 10:21 2023/12/21
     **/
    private String ntMaintainDesciption;

    /**
     * <AUTHOR>
     * @Description PDF等其他地址
     * @Date 10:21 2023/12/21
     **/
    private String ntOtherUrl;

    /**
     * <AUTHOR>
     * @Description 图片地址
     * @Date 10:21 2023/12/21
     **/
    private String ntPicUrl;

    /**
     * <AUTHOR>
     * @Description 富文本数据库持久化字段
     * @Date 10:21 2023/12/21
     **/
    private byte[] richText;

    /**
     * <AUTHOR>
     * @Description 富文本前端请求参数
     * @Date 10:21 2023/12/21
     **/
    private String richContext;

    private String ntInfoId;

    //非持久化字段
    /**
     * <AUTHOR>
     * @Description 前端查询用
     * @Date 10:21 2023/12/21
     **/
    private String ntUrl;
    private String ntInfoUrl;



}
