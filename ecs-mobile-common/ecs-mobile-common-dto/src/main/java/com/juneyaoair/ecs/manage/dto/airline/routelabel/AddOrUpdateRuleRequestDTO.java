package com.juneyaoair.ecs.manage.dto.airline.routelabel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddOrUpdateRuleRequestDTO {

    @ApiModelProperty(value = "标签id,新增需要标签Id", example = "uuid")
    private String labelId;


    @ApiModelProperty(value = "规则id,更新/删除/启用/禁用 需要规则Id", example = "uuid")
    private String ruleId;


    @ApiModelProperty(value = "规则名称", example = "")
    @NotBlank(message = "规则名称不能为空")
    private String labelRuleName;


    @NotBlank(message = "航线日期-开始不能为空")
    @ApiModelProperty(value = "航线日期-开始", example = "yyyy-MM-dd HH:mm:ss")
    private String routeStartDate;

    @NotBlank(message = "航线日期-结束不能为空")
    @ApiModelProperty(value = "航线日期-结束", example = "yyyy-MM-dd HH:mm:ss")
    private String routeEndDate;


    @ApiModelProperty(value = "起飞机场;' / '拼接", example = "BUH/LCA/IST/IZM/CAI/BEY/TIA/SOF")
    private String depAirport;


    @ApiModelProperty(value = "起飞机场航站楼;' / '拼接", example = "T1/T2")
    private String depTerminal;


    @ApiModelProperty(value = "起飞国家;' / '拼接", example = "CN/HK/MO")
    private String depCountry;


    @ApiModelProperty(value = "起飞地区;' / '拼接", example = "EU")
    private String depRegion;


    @ApiModelProperty(value = "到达机场;' / '拼接", example = "BUH/LCA/IST/IZM/CAI/BEY/TIA/SOF")
    private String arrAirport;


    @ApiModelProperty(value = "到达机场航站楼;' / '拼接", example = "T1/T2")
    private String arrTerminal;


    @ApiModelProperty(value = "到达国家;' / '拼接", example = "CN/HK/MO")
    private String arrCountry;


    @ApiModelProperty(value = "到达地区;' / '拼接", example = "EU")
    private String arrRegion;


    @ApiModelProperty(value = "承运航司;两段' - '拼接", example = "HO-AY")
    private String carrier;


    @NotBlank(message = "是否中转不能为空")
    @ApiModelProperty(value = "是否中转;是/否,1/0", example = "1")
    private String transit;


    @ApiModelProperty(value = "中转机场;' / '拼接", example = "BUH/LCA/IST/IZM/CAI/BEY/TIA/SOF")
    private String transAirport;


    @ApiModelProperty(value = "中转出发航站楼;' / '拼接", example = "T2")
    private String transDepTerminal;


    @ApiModelProperty(value = "中转要求同场转机;需同场转机/要求不同机场/无限制，Y/N/A", example = "Y")
    private String transSameAirport;


    @ApiModelProperty(value = "中转时长要求-分钟;x-xxxx", example = "360-2880")
    private String transTime;


    @ApiModelProperty(value = "中转日期限制;隔夜-overnight 当日-sameDay  多个使用英文逗号分割", example = "overnight")
    private String transDateLimit;


    @ApiModelProperty(value = "中转前序航班日期间隔限制;null表示无限制 0-表示当日内 1-表示允许跨一天", example = "0")
    private Short transPreFlightDateLimit;


    @ApiModelProperty(value = "中转后序航班日期间隔限制;null表示无限制 0-表示当日内 1-表示允许跨一天", example = "1")
    private Short transNextFlightDateLimit;


    @NotNull(message = "启用状态不能为空")
    @ApiModelProperty(value = "启用状态;启用/禁用", example = "true")
    private boolean enableStatus;


    @ApiModelProperty(value = "排序", example = "1")
    @NotNull(message = "排序号不能为空")
    private Integer sortNum;


    @ApiModelProperty(value = "备注", example = "无")
    private String remark;



}
