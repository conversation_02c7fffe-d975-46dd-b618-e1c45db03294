package com.juneyaoair.ecs.manage.enums;

import java.util.HashMap;
import java.util.Map;

public enum HK_MO_TWCityEnum {
    TPE("TPE", "中国台北"),
    MFM("MFM", "中国澳门"),
    HKG("HKG", "中国香港"),
    KHH("KHH", "中国高雄"),
    MZG("MZG", "澎湖"),

    ;


    private final String code;
    private final String desc;

    HK_MO_TWCityEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static HK_MO_TWCityEnum getEnumByCode(String code) {
        return s_map.getOrDefault(code, null);
    }

    private static Map<String, HK_MO_TWCityEnum> s_map = new HashMap<>();

    static {
        for (HK_MO_TWCityEnum value : HK_MO_TWCityEnum.values()) {
            s_map.put(value.code, value);
        }
    }
}
