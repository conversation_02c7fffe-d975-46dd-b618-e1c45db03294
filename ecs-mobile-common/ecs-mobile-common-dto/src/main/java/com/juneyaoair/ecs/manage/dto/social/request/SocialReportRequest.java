package com.juneyaoair.ecs.manage.dto.social.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SocialReportRequest
 * @Description 社会责任报告请求体
 * <AUTHOR>
 * @Date 2024/6/12 16:40
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SocialReportRequest {
    private Integer id;

    @ApiModelProperty(value = "渠道号")
    private String channelCode;

    @ApiModelProperty(value = "渠道号", notes = "N-已删除 Y-有效")
    private String status;
}
