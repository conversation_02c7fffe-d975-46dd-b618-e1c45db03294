package com.juneyaoair.ecs.manage.dto.activity.response.osaka;

import com.alibaba.excel.annotation.ExcelProperty;

import java.util.Date;

/**
 * @ClassName OsakaCouponClaimResponse
 * @Description 券码列表返回体
 * <AUTHOR>
 * @Date 2025/6/23 14:08
 * @Version 1.0
 */
public class OsakaCouponClaimResponse {

    @ExcelProperty(value = "活动号", index = 0)
    private String merchantCode;

    @ExcelProperty(value = "会员卡号", index = 1)
    private String ffpCardNo;

    @ExcelProperty(value = "票号", index = 2)
    private String ticketNo;

    @ExcelProperty(value = "航班号", index = 3)
    private String flightNo;

    @ExcelProperty(value = "航班日期", index = 4)
    private String flightDate;

    @ExcelProperty(value = "出发机场三字码", index = 5)
    private String deptAirPortCode;

    @ExcelProperty(value = "出发机场名", index = 6)
    private String deptAirPortName;

    @ExcelProperty(value = "到达机场三字码", index = 7)
    private String arrAirPortCode;

    @ExcelProperty(value = "到达机场名", index = 8)
    private String arrAirPortName;

    @ExcelProperty(value = "舱位等级", index = 9)
    private String cabinClass;

    @ExcelProperty(value = "手机号", index = 10)
    private String phoneNumber;//需要脱敏

    @ExcelProperty(value = "券码类型", index = 11)
    private String couponType;

    @ExcelProperty(value = "券码", index = 12)
    private String couponCode;

    @ExcelProperty(value = "券码名称", index = 13)
    private String couponName;

    @ExcelProperty(value = "券码有效期开始日期", index = 14)
    private String couponValidStartTime;

    @ExcelProperty(value = "券码有效期结束日期", index = 15)
    private String couponValidEndTime;

    @ExcelProperty(value = "券码状态", index = 16)
    private String couponStatus;

    @ExcelProperty(value = "领取时间", index = 17)
    private String claimTime;

    @ExcelProperty(value = "推荐人", index = 18)
    private String recordSource;

    public OsakaCouponClaimResponse() {
    }

    public OsakaCouponClaimResponse(String merchantCode, String ffpCardNo, String ticketNo, String flightNo, String flightDate, String deptAirPortCode, String deptAirPortName, String arrAirPortCode, String arrAirPortName, String cabinClass, String phoneNumber, String couponType, String couponCode, String couponName, String couponValidStartTime, String couponValidEndTime, String couponStatus, String claimTime, String recordSource) {
        this.merchantCode = merchantCode;
        this.ffpCardNo = ffpCardNo;
        this.ticketNo = ticketNo;
        this.flightNo = flightNo;
        this.flightDate = flightDate;
        this.deptAirPortCode = deptAirPortCode;
        this.deptAirPortName = deptAirPortName;
        this.arrAirPortCode = arrAirPortCode;
        this.arrAirPortName = arrAirPortName;
        this.cabinClass = cabinClass;
        this.phoneNumber = phoneNumber;
        this.couponType = couponType;
        this.couponCode = couponCode;
        this.couponName = couponName;
        this.couponValidStartTime = couponValidStartTime;
        this.couponValidEndTime = couponValidEndTime;
        this.couponStatus = couponStatus;
        this.claimTime = claimTime;
        this.recordSource = recordSource;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getDeptAirPortCode() {
        return deptAirPortCode;
    }

    public void setDeptAirPortCode(String deptAirPortCode) {
        this.deptAirPortCode = deptAirPortCode;
    }

    public String getDeptAirPortName() {
        return deptAirPortName;
    }

    public void setDeptAirPortName(String deptAirPortName) {
        this.deptAirPortName = deptAirPortName;
    }

    public String getArrAirPortCode() {
        return arrAirPortCode;
    }

    public void setArrAirPortCode(String arrAirPortCode) {
        this.arrAirPortCode = arrAirPortCode;
    }

    public String getArrAirPortName() {
        return arrAirPortName;
    }

    public void setArrAirPortName(String arrAirPortName) {
        this.arrAirPortName = arrAirPortName;
    }

    public String getCabinClass() {
        return cabinClass;
    }

    public void setCabinClass(String cabinClass) {
        this.cabinClass = cabinClass;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getCouponValidStartTime() {
        return couponValidStartTime;
    }

    public void setCouponValidStartTime(String couponValidStartTime) {
        this.couponValidStartTime = couponValidStartTime;
    }

    public String getCouponValidEndTime() {
        return couponValidEndTime;
    }

    public void setCouponValidEndTime(String couponValidEndTime) {
        this.couponValidEndTime = couponValidEndTime;
    }

    public String getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(String couponStatus) {
        this.couponStatus = couponStatus;
    }

    public String getClaimTime() {
        return claimTime;
    }

    public void setClaimTime(String claimTime) {
        this.claimTime = claimTime;
    }

    public String getRecordSource() {
        return recordSource;
    }

    public void setRecordSource(String recordSource) {
        this.recordSource = recordSource;
    }
}
