package com.juneyaoair.ecs.manage.dto.activity.request;

import com.juneyaoair.ecs.manage.dto.activity.common.CityLabelInfo;
import com.juneyaoair.ecs.manage.dto.activity.common.DstWtInfo;
import com.juneyaoair.ecs.manage.dto.activity.common.FileInfo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 城市结构返回体
 * @Date 10:24 2023/12/28
 **/
@Data
public class CityInfo implements Comparable<CityInfo> {

    private String cityCode;
    private String cityName;
    private String cityEName;
    private String countryCode;
    private String countryNm;
    private String cityPinYin;
    private String cityPinYinAbb;
    private String isHotCity;
    private String isTopCity;
    private String cityTimeZone;
    private String isInternational;
    private String url;
    private BigDecimal cityHotOrder;
    //保存時文件的ID
    private String[] fileID;
    private List<FileInfo> fileInfos;

    private String arrCity;
    private String depCity;

    private String airportCode;
    private String airportName;
    private String airportEName;
    /**
     * 特色iconUrl
     */
    private String iconUrl;
    /**
     * 城市标签对象
     */
    @Transient
    private List<CityLabelInfo> cityLabelInfos;
    @Transient
    private DstWtInfo dstWtInfo;
    private BigDecimal provinceId;
    private String provinceName;
    private String officeAddress;
    private String officeTel;
    private Date createDatetime;
    private Long createId;
    private String nameAbb;
    private String englishNameAbb;
    private String baidumappoint;
    private String officeFax;
    private String delflag;
    private String dstWtId;
    private String cityKoName;

    private String cityJpName;

    private String cityThName;

    private String cityTcName;

    @Override
    public int compareTo(CityInfo o) {
        if (StringUtils.isEmpty(this.cityCode)) {
            return -1;
        }
        return cityCode.compareTo(o.cityCode);
    }
}
