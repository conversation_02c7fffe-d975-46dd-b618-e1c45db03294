package com.juneyaoair.ecs.manage.enums;

/**
 * <AUTHOR>
 * @description 公告模板枚举
 * @date 2023/5/17 11:17
 */
public enum NoticeTemplateEnum {
    MOBILE("mobile_notice.html","mobile"),
    B2C("b2c_notice.html","b2c"),
    ;

    /**
     * 模板名称
     */
    private String name;
    /**
     * 对应生成的静态资源目录
     */
    private String dir;

    NoticeTemplateEnum(String name, String dir) {
        this.name = name;
        this.dir = dir;
    }

    public String getName() {
        return name;
    }

    public String getDir() {
        return dir;
    }
}
