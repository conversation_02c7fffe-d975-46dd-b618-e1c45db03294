package com.juneyaoair.ecs.manage.dto.notice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeInfo {
    private String ntInfoId;
    private String noticeId;
    private String ntInfoName;
    private String ntInfoUrl;
    private String createTime;
    private String modifyTime;
    private String person;
    /**
     * <AUTHOR>
     * @Description 一级模块编码
     * @Date 9:09 2023/12/21
     **/
    private String modularBm;

    /**
     * <AUTHOR>
     * @Description 一级模块名称
     * @Date 9:09 2023/12/21
     **/
    private String dtName;

    /**
     * <AUTHOR>
     * @Description 二级模块编码
     * @Date 9:09 2023/12/21
     **/
    private String noticeBm;

    /**
     * <AUTHOR>
     * @Description 二级模块名称
     * @Date 9:09 2023/12/21
     **/
    private String ntName;

    /**
     * <AUTHOR>
     * @Description 显示序号
     * @Date 9:09 2023/12/21
     **/
    private String ntInfoCode;

    /**
     * <AUTHOR>
     * @Description 备注
     * @Date 9:09 2023/12/21
     **/
    private String ntInfoDescription;

    /**
     * <AUTHOR>
     * @Description 显示标记
     * @Date 9:09 2023/12/21
     **/
    private String canInfoShow;

    /**
     * <AUTHOR>
     * @Description 显示标记
     * @Date 9:09 2023/12/21
     **/
    private boolean getRichtext;

    /**
     * <AUTHOR>
     * @Description 接收参数
     * @Date 9:09 2023/12/21
     **/
    private String richContext;

    /**
     * <AUTHOR>
     * @Description 富文本
     * @Date 9:09 2023/12/21
     **/
    private byte[] richText;

    //详情表相关参数
    /**
     * <AUTHOR>
     * @Description PDF等其他地址
     * @Date 9:09 2023/12/21
     **/
    private String ntOtherUrl;

    /**
     * <AUTHOR>
     * @Description 图片地址(条款附件地址) 首先返回此地址 其次再使用ntInfoUrl
     * @Date 9:09 2023/12/21
     **/
    private String ntPicUrl;

    /**
     * <AUTHOR>
     * @Description 模块名称
     * @Date 9:09 2023/12/21
     **/
    private String ntMaintainName;

    /**
     * <AUTHOR>
     * @Description 模块描述
     * @Date 9:09 2023/12/21
     **/
    private String ntMaintainDesciption;

    private String maintainId;

    /**
     * <AUTHOR>
     * @Description 富文本信息
     * @Date 9:09 2023/12/21
     **/
    private byte[] richTexts;

}
