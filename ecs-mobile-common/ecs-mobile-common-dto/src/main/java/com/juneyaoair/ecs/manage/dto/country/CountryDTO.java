package com.juneyaoair.ecs.manage.dto.country;


import lombok.Data;

import java.util.Date;

@Data
public class CountryDTO {
    /**
     * 国家代码
     */
    public String countryCode;

    /**
     * 国家中文名
     */
    public String countryName;

    /**
     * 国家英文名
     */
    public String englishName;

    /**
     * 币种
     */
    public String currency;

    /**
     * 创建时间
     */
    public Date createDatetime;

    /**
     * 创建id
     */
    public Long creatorId;

    /**
     * 国家地区电话前缀
     */
    public String countryTelCode;

    /**
     * 热门国家，Y/N
     */
    public String hotCountry;

    /**
     * 序号
     */
    public Integer sequence;

    /**
     * 所属洲CODE，关联region表
     */
    public String regionCode;


    //以下为region表里字段
    public String regionName;
    public String regionEName;


}