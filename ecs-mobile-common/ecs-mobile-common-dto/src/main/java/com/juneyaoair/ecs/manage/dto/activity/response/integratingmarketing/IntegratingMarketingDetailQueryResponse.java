package com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName IntegratingMarketingDetailQueryResponse
 * @Description
 * <AUTHOR>
 * @Date 2025/6/29 13:29
 * @Version 1.0
 */
@Data
public class IntegratingMarketingDetailQueryResponse {
    /**
     * <AUTHOR>
     * @Description 员工域账号
     * @Date 13:30 2025/6/29
     **/
    @ExcelProperty(value = "员工域账号", index = 0)
    private String domainAccount;

    /**
     * <AUTHOR>
     * @Description 旅客会员卡号
     * @Date 13:30 2025/6/29
     **/
    @ExcelProperty(value = "旅客会员卡号", index = 1)
    private String passengerCardNo;

    /**
     * <AUTHOR>
     * @Description 部门
     * @Date 13:30 2025/6/29
     **/
    @ExcelProperty(value = "部门", index = 2)
    private String department;

    /**
     * <AUTHOR>
     * @Description 关联时间
     * @Date 13:30 2025/6/29
     **/
    @ExcelProperty(value = "关联时间", index = 3)
    private String associatedTime;

    /**
     * <AUTHOR>
     * @Description 国际总飞行次数
     * @Date 13:30 2025/6/29
     **/
    @ExcelProperty(value = "国际总飞行次数", index = 4)
    private Long internationalTripTimes;

    /**
     * <AUTHOR>
     * @Description 国内总飞行次数
     * @Date 13:30 2025/6/29
     **/
    @ExcelProperty(value = "国内总飞行次数", index = 5)
    private Long domesticTripTimes;

    /**
     * <AUTHOR>
     * @Description 总飞行次数
     * @Date 13:30 2025/6/29
     **/
    @ExcelProperty(value = "总飞行次数", index = 6)
    private Long totalTripTimes;

    /**
     * <AUTHOR>
     * @Description 奖励合计
     * @Date 13:30 2025/6/29
     **/
    @ExcelProperty(value = "奖励合计", index = 7)
    private Long totalRewards;

}
