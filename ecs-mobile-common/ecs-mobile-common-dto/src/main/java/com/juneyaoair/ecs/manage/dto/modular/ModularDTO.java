package com.juneyaoair.ecs.manage.dto.modular;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(value = "ModularDTO", description = "服务配置信息")
public class ModularDTO {
    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("模块名称")
    private String name;

    @ApiModelProperty("地址")
    private String url;

    @ApiModelProperty("图片地址")
    private String iconurl;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("是否可用")
    private String isUsed;

    @ApiModelProperty("父模块编码")
    private String modularBm;

    @ApiModelProperty("父模块名称")
    private String modularName;

    @ApiModelProperty("序号")
    private Short orderNo;

    @ApiModelProperty("说明")
    private String remark;

    @ApiModelProperty("ios")
    private String ios;

    @ApiModelProperty("android")
    private String android;

    @ApiModelProperty("harmony")
    private String harmony;

    @ApiModelProperty("事件模块")
    private String eventModule;

    @ApiModelProperty("事件类型")
    private String eventType;

    @ApiModelProperty("窗口名称")
    private String winName;

    @ApiModelProperty("二级名称")
    private String secondary;

    @ApiModelProperty("显示new标记")
    private String displayNew;

    @ApiModelProperty("渠道")
    private String channelCode;

    @ApiModelProperty("是否需要登录，Y需要，N不需要")
    private String loginFlag;

    @ApiModelProperty("角标图标地址")
    private String cornerIconUrl;

} 