package com.juneyaoair.ecs.manage.dto.activity.request;

public class SubPrizeReq {
    private String id;

    /**
     * 子奖品编码(奖品发放编码)
     */
    private String subPrizeCode;

    /**
     * 子奖品类别 类型定义参照基础服务 PRIZE_TYPE_ENUM
     */
    private String subPrizeCategory;

    /**
     * 子奖品名称
     */
    private String subPrizeName;

    /**
     * 子奖品数量
     */
    private Integer subPrizeAmount;

    public SubPrizeReq() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSubPrizeCode() {
        return subPrizeCode;
    }

    public void setSubPrizeCode(String subPrizeCode) {
        this.subPrizeCode = subPrizeCode;
    }

    public String getSubPrizeCategory() {
        return subPrizeCategory;
    }

    public void setSubPrizeCategory(String subPrizeCategory) {
        this.subPrizeCategory = subPrizeCategory;
    }

    public String getSubPrizeName() {
        return subPrizeName;
    }

    public void setSubPrizeName(String subPrizeName) {
        this.subPrizeName = subPrizeName;
    }

    public Integer getSubPrizeAmount() {
        return subPrizeAmount;
    }

    public void setSubPrizeAmount(Integer subPrizeAmount) {
        this.subPrizeAmount = subPrizeAmount;
    }
}
