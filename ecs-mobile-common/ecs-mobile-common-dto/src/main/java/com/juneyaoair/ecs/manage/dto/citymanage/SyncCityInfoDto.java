package com.juneyaoair.ecs.manage.dto.citymanage;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SyncCityInfoDto {
    public String region;
    public String cityCode;
    public String cityName;
    @ApiModelProperty(value = "城市名称简称")
    public String nameAbb;

    @ApiModelProperty(value = "城市关键字")
    public String cityKeyWords;
    public String cityEName;
    public String cityPinYin;
    public String cityPinYinAbb;
    public String countryCode;
    public String isHotCity;
    public String cityTimeZone;
    public String isInternational;
    public String cityHotOrder;
    public String zoneHotOrder;
    /**
     * 特色iconUrl
     */
    public String iconUrl;
    public List<CityLabelInfo> cityLabelInfoList;//城市标签列表
    /**
     * 热门地区
     */
    public String isHotRegion;
    /**
     * 城市图片
     */
    public String cityPic;
    //经度
    public String longitude;
    //纬度
    public String latitude;
    /**
     * 是否常用城市
     */
    public String isOftenCity;

    public String countryName;
    public String provinceName;

    @ApiModelProperty("城市名称（语言：值）")
    public Map<String, String> cityNameMap;
}
