package com.juneyaoair.ecs.manage.dto.activity.response;

import com.juneyaoair.ecs.manage.dto.activity.common.HolidayCalendar;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName HolidayCalenderResponse
 * @Description
 * <AUTHOR>
 * @Date 2024/1/22 13:18
 * @Version 1.0
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HolidayCalenderResponse {
    @ApiModelProperty(value = "结果代码1001 － 成功，其它失败")
    private String resultCode;

    @ApiModelProperty(value = "错误信息")
    private String errorInfo;

    @ApiModelProperty(value = "节假日列表")
    private List<HolidayCalendar> holidayCalendarList;
}
