package com.juneyaoair.ecs.manage.enums.activity.redeem;

import lombok.Getter;

@Getter
public enum RedeemActPointEnum {


    SIGN_POINT("SIGN", "2023-积分兑换活动-签到积分"),

    SEG_NORMAL("SEG_NORMAL", "2023-积分兑换活动-飞行券"),

    SEG_PRO("SEG_PRO", "2023-积分兑换活动-飞行券PRO");


    /**
     * 业务模块
     */
    private String pointType;
    /**
     * 业务模块描述
     */
    private String desc;

    RedeemActPointEnum(String actType, String desc) {
        this.pointType = actType;
        this.desc = desc;
    }


}
