package com.juneyaoair.ecs.manage.dto.tongdun;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/10 17:29
 */
@Data
public class FraudApiResponse {
    private Boolean success = false;                // 执行是否成功，不成功时对应reason_code
    private String reason_code;                                // 错误码及原因描述，正常执行完扫描时为空
    private Integer final_score;                                // 风险分数
    private String final_decision;                            // 最终的风险决策结果
    private String policy_name;                                // 策略名称
    private List<HitRule> hit_rules = new ArrayList<>();    // 命中规则列表
    private String seq_id;                                    // 请求序列号，每个请求进来都分配一个全局唯一的id
    private Integer spend_time;                                // 花费的时间，单位ms
    private Map<String, String> geoip_info = new HashMap<>();        // 地理位置信息
    private Map<String, Object> device_info = new HashMap<>();        // 设备指纹信息
    private Map<String, Object> attribution = new HashMap<>();      // 归属地信息
    private List<Policy> policy_set = new ArrayList<>();    // 策略集信息
    private String policy_set_name;                            // 策略集名称
    private String risk_type;                                // 风险类型
    /**
     * 虚拟IP
     */
    private String virtualIp;
    /**
     * 原因描述
     */
    private String reason_desc;
    /**
     * 备注说明
     */
    private String remark;
}
