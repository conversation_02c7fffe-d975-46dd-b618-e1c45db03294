package com.juneyaoair.ecs.manage.dto.citymanage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class CityInfoReqDTO {
    @ApiModelProperty("城市名称")
    private String cityName;
    @ApiModelProperty("城市code")
    private String cityCode;
    @ApiModelProperty("城市类型")
    private String isInternational;
    @ApiModelProperty("热门城市")
    private String isHotCity;
    @ApiModelProperty("常用城市")
    private String isOftenCity;
    @ApiModelProperty("热门地区")
    private String isHotRegion;
    @ApiModelProperty(value = "启用状态",example = "1",allowableValues = "1,0")
    private String status;
}
