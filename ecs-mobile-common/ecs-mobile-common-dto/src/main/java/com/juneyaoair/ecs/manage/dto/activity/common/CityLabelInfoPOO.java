package com.juneyaoair.ecs.manage.dto.activity.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName CityLabelInfoPOO
 * @Description
 * <AUTHOR>
 * @Date 2023/12/28 14:41
 * @Version 1.0
 */
@Data
@TableName("T_CITY_LABEL_INFO")
public class CityLabelInfoPOO {

    @TableField(value = "CITY_LABEL_ID")
    public Integer cityLabelId;

    @TableField(value = "CITY_CODE")
    public String cityCode;

    @TableField(value = "CITY_LABEL_NAME")
    public String cityLabelName;

    @TableField(value = "CITY_LABEL_URL")
    public String cityLabelUrl;

    @TableField(value = "UPDATETIME")
    public Date updateTime;

    @TableField(value = "UPDATE_USER")
    public String updateUser;

    @TableField(value = "CREATE_USER")
    public String createUser;

    @TableField(value = "CREATETIME")
    public Date createTime;

    @TableField(value = "LABEL_INTRODUCE")
    public String labelIntroduce;

    @TableField(value = "CITY_LABEL_PC_URL")
    public String cityLabelPcUrl;

}
