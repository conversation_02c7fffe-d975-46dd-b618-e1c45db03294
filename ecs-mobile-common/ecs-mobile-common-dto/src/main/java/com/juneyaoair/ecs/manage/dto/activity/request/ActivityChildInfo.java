package com.juneyaoair.ecs.manage.dto.activity.request;

import com.juneyaoair.ecs.manage.dto.activity.common.ActivityAirLine;
import com.juneyaoair.ecs.manage.dto.activity.common.ActivityCommodity;
import com.juneyaoair.ecs.manage.dto.activity.common.ActivityInnerPage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActivityChildInfo {
    private String id;
    @NotEmpty(message="礼包名称不能为空")
    private String dataType;
    @NotEmpty(message="开始时间不能为空")
    private String dataName;
    @NotEmpty(message="结束时间不能为空")
    private String linkName;
    private String dataDesc;
    private String startDate;
    private String endDate;
    private String baseinfoId;
    private String createMan;
    private String createTime;
    private String updateMan;
    private String updateTime;
    private String isValid;
    private String activityName;
    private String activityStartDate;
    private String activityEndDate;
    private String seckillStartTime;
    private String seckillEndTime;
    private List<ActivityAirLine> airlineList;
    private List<ActivityCommodity> commodities;
    private List<ActivityInnerPage> innerPages;
}
