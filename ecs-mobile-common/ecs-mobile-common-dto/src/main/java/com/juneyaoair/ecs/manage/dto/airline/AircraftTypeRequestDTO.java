package com.juneyaoair.ecs.manage.dto.airline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> <EMAIL>
 * @version v1.0
 * @date 2023/8/22 8:28
 * @description
 **/
@ApiModel
@Data
public class AircraftTypeRequestDTO {

    @ApiModelProperty("机型名称")
    public String aircraftTypeCode;

    @ApiModelProperty("状态 Y:启用 N：禁用")
    public String status;

}
