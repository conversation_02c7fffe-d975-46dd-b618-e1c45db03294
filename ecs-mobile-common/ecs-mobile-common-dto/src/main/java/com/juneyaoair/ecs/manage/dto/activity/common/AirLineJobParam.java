package com.juneyaoair.ecs.manage.dto.activity.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AirLineJobParam {
    private String area;
    private String activityParam;
    private String jobStartDate;
    private String jobEndDate;
    private List<AirLineChild> airline;
}
