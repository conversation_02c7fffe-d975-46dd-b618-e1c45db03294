package com.juneyaoair.ecs.manage.dto.aircraft;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @version v1.0
 * @date 2023/8/22 8:25
 * @description
 **/

@ApiModel
@Data
public class AircraftTypeDTO {
    @ApiModelProperty("主键ID")
    private String aircraftTypeId;

    @ApiModelProperty("机型编码")
    private String aircraftTypeCode;

    @ApiModelProperty("机型名称")
    private String aircraftTypeName;

    @ApiModelProperty("机型图片")
    private String aircraftIcon;

    @ApiModelProperty("公务舱数量")
    private Integer businessClassNum;

    @ApiModelProperty("经济舱数量")
    private Integer economyClassNum;

    @ApiModelProperty(value = "状态 Y启用 N禁用")
    public String status;

    @ApiModelProperty("创建人")
    public String createUser;

    @ApiModelProperty("创建时间")
    public Date createtime;

    @ApiModelProperty("更新人")
    public String updateUser;

    @ApiModelProperty("更新时间")
    public Date updatetime;

    @ApiModelProperty("机型映射")
    public List<AircraftMappingDTO> aircraftMappingList;

}
