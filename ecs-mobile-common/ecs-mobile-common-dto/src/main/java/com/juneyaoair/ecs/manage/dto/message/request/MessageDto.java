package com.juneyaoair.ecs.manage.dto.message.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 消息创建请求
 * @date 2023/5/10 15:03
 */
@Data
@ApiModel(value = "MessageDto", description = "公告请求对象")
public class MessageDto {
    @ApiModelProperty("记录唯一编号")
    private String messageid;

    @NotBlank(message = "公告标题不可为空")
    @ApiModelProperty("公告标题")
    private String messageTitle;

    @ApiModelProperty("公告创建人")
    private String messageAuth;

    @NotBlank(message = "文本内容不可为空")
    @ApiModelProperty("公告富文本格式内容")
    private String messageContent;

    @NotBlank(message = "公告内容纯文本不可为空")
    @ApiModelProperty("公告消息纯文本内容")
    private String messagePlaintxt;

    @ApiModelProperty(value = "消息类型",allowableValues = "MSG_CD_NOTICE" )
    private String messageType;

    private String messagePublishman;
    private String messagePublishtime;

    private String messagePicUrl;

    @ApiModelProperty("发布状态")
    private String messageStatus;

    @ApiModelProperty("推送状态")
    private String messageSendornot;

    @NotBlank(message = "公告生效时间不可为空")
    @ApiModelProperty("公告生效时间")
    private String messageStarttime;

    @NotBlank(message = "公告失效时间不可为空")
    @ApiModelProperty("公告失效时间")
    private String messageEndtime;

    @ApiModelProperty("公告更新时间")
    private Date messageUpdatetime;

    @ApiModelProperty("公告更新人")
    private String messageUpdateman;

    @ApiModelProperty("移动端公告跳转地址")
    private String messageUrl;

    @ApiModelProperty("官网公告跳转地址")
    private String b2cMessageUrl;

    private String messageInfoType;

    @ApiModelProperty("是否允许消息推送")
    private String messageTypeCode;

    private String messageInfoTypeCode;

    private String messageStatusCode;

    @ApiModelProperty(value = "是否可以首页展示",allowableValues = "Y,N",notes = "Y-首页展示，N-不首页展示")
    private String messageIstop;

    @ApiModelProperty("消息推送标题")
    private String messagePushtitle;

    private String messageIsannouncement;

    @ApiModelProperty("标签id")
    private String messageTagId;

    @ApiModelProperty("消息标签名称")
    private String messageTypeName;

    @ApiModelProperty("语言")
    private String language;

    @ApiModelProperty(value = "渠道列表",allowableValues = "B2C,MOBILE,WEIXIN,WXAPP")
    @Size(min = 1,message = "请至少选择一个渠道")
    @NotEmpty(message = "渠道不可为空")
    private List<String> channelList;

    @ApiModelProperty("服务配置ID")
    private List<String> serviceIdList;

}
