package com.juneyaoair.ecs.manage.enums.activity.redeem;

public enum RedeemActStateEnum {


    DELETE("0", "删除"),
    NEW("1", "新增"),
    AUDITED("2", "已审核");


    /**
     * 业务模块
     */
    private String actState;
    /**
     * 业务模块描述
     */
    private String desc;

    RedeemActStateEnum(String actState, String desc) {
        this.actState = actState;
        this.desc = desc;
    }


    public String getActState() {
        return actState;
    }

    public String getDesc() {
        return desc;
    }


}
