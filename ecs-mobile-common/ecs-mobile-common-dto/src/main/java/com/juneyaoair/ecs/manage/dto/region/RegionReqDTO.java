package com.juneyaoair.ecs.manage.dto.region;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@ApiModel
@Getter
@Setter
public class RegionReqDTO {
    @ApiModelProperty("洲code")
    public String regionCode;

    /**
     * 洲名
     */
    @ApiModelProperty("洲名")
    public String regionName;

    /**
     * 洲英文名
     */
    @ApiModelProperty("洲英文名")
    public String regionEName;

    @ApiModelProperty("createTime")
    public Date createTime;

    @ApiModelProperty("createUser")
    public String createUser;

    @ApiModelProperty("lastUpdateTime")
    public Date lastUpdateTime;

    @ApiModelProperty("lastUpdateUser")
    public String lastUpdateUser;
}
