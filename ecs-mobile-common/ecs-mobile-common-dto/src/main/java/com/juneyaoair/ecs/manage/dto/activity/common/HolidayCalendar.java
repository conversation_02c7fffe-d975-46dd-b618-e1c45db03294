package com.juneyaoair.ecs.manage.dto.activity.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 17:18 2017/12/25
 * @Modified by:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HolidayCalendar implements Serializable {
    @ApiModelProperty(value = "记录唯一键")
    private String id;

    @ApiModelProperty(value = "休/班")
    private String name;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "假日名称")
    private String holidayName;

    @ApiModelProperty(value = "冗余字段")
    private String extraField;

    @ApiModelProperty(value = "描述")
    private String desc;

    @ApiModelProperty(value = "是否显示在日期框")
    private String display;
}
