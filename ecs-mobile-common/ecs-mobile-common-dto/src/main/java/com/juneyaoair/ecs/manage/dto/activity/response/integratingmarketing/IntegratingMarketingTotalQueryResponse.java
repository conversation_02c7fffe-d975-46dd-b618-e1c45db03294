package com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName IntegratingMarketingTotalQueryResponse
 * @Description
 * <AUTHOR>
 * @Date 2025/6/29 10:58
 * @Version 1.0
 */
@Data
public class IntegratingMarketingTotalQueryResponse {

    /**
     * <AUTHOR>
     * @Description 员工工号
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "员工工号", index = 0)
    private String employeeNumber;

    /**
     * <AUTHOR>
     * @Description 员工姓名
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "员工姓名", index = 1)
    private String employeeName;

    /**
     * <AUTHOR>
     * @Description 所属部门
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "所属部门", index = 2)
    private String department;

    /**
     * <AUTHOR>
     * @Description 员工域账号
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "员工域账号", index = 3)
    private String domainAccount;

    /**
     * <AUTHOR>
     * @Description 员工域账号
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "员工卡号", index = 4)
    private String employeeCardNo;

    /**
     * <AUTHOR>
     * @Description 首次拉新奖励发放状态
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "首次拉新奖励发放状态", index = 5)
    private String newCustomerStatus;

    /**
     * <AUTHOR>
     * @Description 首次出行奖励发放状态
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "首次出行奖励发放状态", index = 6)
    private String firstTripStatus;

    /**
     * <AUTHOR>
     * @Description 总出行次数
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "总出行次数", index = 7)
    private Long totalTripTimes;

    /**
     * <AUTHOR>
     * @Description 国际出行次数
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "国际出行次数", index = 8)
    private Long internationalTripTimes;

    /**
     * <AUTHOR>
     * @Description 国内出行次数
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "国内出行次数", index = 9)
    private Long domesticTripTimes;

    /**
     * <AUTHOR>
     * @Description 奖励合计
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "奖励合计", index = 10)
    private Long totalRewards;

    /**
     * <AUTHOR>
     * @Description 奖励合计
     * @Date 10:56 2025/6/29
     **/
    @ExcelProperty(value = "营销人数", index = 11)
    private Long developedCustomer;

}
