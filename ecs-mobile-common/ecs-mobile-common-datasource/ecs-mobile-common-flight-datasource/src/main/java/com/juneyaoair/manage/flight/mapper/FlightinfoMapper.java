package com.juneyaoair.manage.flight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juneyaoair.manage.flight.annotation.Flight;
import com.juneyaoair.manage.flight.entity.Flightinfo;

import java.util.List;

/**
 * <p>
 * 航班信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Flight
public interface FlightinfoMapper extends BaseMapper<Flightinfo> {
     /**
      * 查询航班信息列表
      * @param flightinfo
      * @return
      */
     List<Flightinfo> selectFlightList(Flightinfo flightinfo);

}
