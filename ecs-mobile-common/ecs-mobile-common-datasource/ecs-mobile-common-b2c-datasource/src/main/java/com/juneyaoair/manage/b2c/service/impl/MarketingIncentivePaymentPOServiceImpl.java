package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.MarketingIncentivePaymentPO;
import com.juneyaoair.manage.b2c.service.MarketingIncentivePaymentPOService;
import com.juneyaoair.manage.b2c.mapper.MarketingIncentivePaymentPOMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【T_MARKETING_INCENTIVE_PAYMENT(全员营销奖励发放流水表)】的数据库操作Service实现
* @createDate 2025-06-29 11:45:33
*/
@Service
public class MarketingIncentivePaymentPOServiceImpl extends ServiceImpl<MarketingIncentivePaymentPOMapper, MarketingIncentivePaymentPO>
    implements MarketingIncentivePaymentPOService{

}




