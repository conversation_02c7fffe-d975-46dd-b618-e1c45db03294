package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.ActivityCouponDetailPO;
import com.juneyaoair.manage.b2c.service.IActivityCouponDetailService;
import com.juneyaoair.manage.b2c.mapper.ActivityCouponDetailMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_COUPON_DETAIL(优惠券礼包明细)】的数据库操作Service实现
* @createDate 2023-12-11 10:58:49
*/
@Service
public class ActivityCouponDetailServiceImpl extends ServiceImpl<ActivityCouponDetailMapper, ActivityCouponDetailPO>
    implements IActivityCouponDetailService {

}




