package com.juneyaoair.manage.b2c.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juneyaoair.ecs.manage.dto.notice.DICTValue;

import java.util.List;


public interface DictMapper extends BaseMapper<DICTValue> {
    List<DICTValue> getChannels(DICTValue dtid);

    List<DICTValue> findDICTValue(DICTValue dictValue);

    List<DICTValue> findDICTValueList(DICTValue dv);

    int updateDictValue(DICTValue dv);

    int addDictValue(DICTValue dv);

}
