package com.juneyaoair.manage.b2c.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c.entity.activity.ActivityInnerPageInfoPO;
import com.juneyaoair.manage.b2c.service.IActivityInnerPageInfoService;
import com.juneyaoair.manage.b2c.mapper.ActivityInnerPageInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_INNER_PAGE_INFO】的数据库操作Service实现
* @createDate 2023-12-11 09:17:59
*/
@Service
public class ActivityInnerPageInfoServiceImpl extends ServiceImpl<ActivityInnerPageInfoMapper, ActivityInnerPageInfoPO>
    implements IActivityInnerPageInfoService {

    @Autowired
    private ActivityInnerPageInfoMapper activityInnerPageInfoMapper;

    @Override
    public boolean removeById(ActivityInnerPageInfoPO activityInnerPageInfoPO) {
        return activityInnerPageInfoMapper.deleteByChildInfoId(activityInnerPageInfoPO) > 0;
    }

    @Override
    public List<ActivityInnerPageInfoPO> toGainAllById(ActivityInnerPageInfoPO activityInnerPageInfoPO) {
        return activityInnerPageInfoMapper.searchAllById(activityInnerPageInfoPO);
    }
}




