package com.juneyaoair.manage.b2c.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.juneyaoair.manage.b2c.entity.activity.ActivityCommodityPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ACTIVITY_COMMODITY】的数据库操作Mapper
* @createDate 2023-12-08 15:20:53
* @Entity com.juneyaoair.manage.b2c.entity.activity.ActivityCommodity
*/
public interface ActivityCommodityMapper extends BaseMapper<ActivityCommodityPO> {

    int delByChildInfoId(ActivityCommodityPO commodityPO);

    List<ActivityCommodityPO> searchAllById(ActivityCommodityPO activityCommodityPO);



}




