package com.juneyaoair.manage.b2c.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
    * 城市对应疫情跳转信息表
    */
@Data
@TableName("T_EPIDEMIC_INFO")
public class EpidemicInfoPO {
    /**
    * 城市三字码
    */
    @TableId
    private String cityCode;

    /**
    * 跳转类型，URL-跳转url地址，PIC-图片模式
    */
    private String jumpType;

    /**
    * 跳转地址，url
    */
    private String jumpUrl;

    /**
    * 图片模式时，图片说明
    */
    private String picTip;
}