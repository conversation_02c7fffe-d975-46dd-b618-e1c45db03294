package com.juneyaoair.manage.b2c.entity.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 发放非固定数量奖品的许可表
 *
 * @TableName T_ACTIVITY_ISSUE_LICENSE
 */
@Table(name = "T_ACTIVITY_ISSUE_LICENSE")
@SuppressWarnings("all")
public class ActivityIssueLicensePO implements Serializable {
    /**
     * 会员卡号
     */
    @Column(name = "FFP_CARD_NO")
    private String ffpCardNo;

    /**
     * 活动号
     */
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    /**
     * 辅助字段 通常意义上来说 此字段内容为参与某活动的必须条件
     */
    @Column(name = "AUXILIARY_FIELD")
    private String auxiliayField;

    /**
     * 飞行次数
     */
    @Column(name = "FLIGHT_TIMES")
    private Integer flightTimes;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    public ActivityIssueLicensePO() {
    }

    public ActivityIssueLicensePO(String ffpCardNo, String activityCode, String auxiliayField, Integer flightTimes, Date createTime, String createUser, Date updateTime, String updateUser) {
        this.ffpCardNo = ffpCardNo;
        this.activityCode = activityCode;
        this.auxiliayField = auxiliayField;
        this.flightTimes = flightTimes;
        this.createTime = createTime;
        this.createUser = createUser;
        this.updateTime = updateTime;
        this.updateUser = updateUser;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getAuxiliayField() {
        return auxiliayField;
    }

    public void setAuxiliayField(String auxiliayField) {
        this.auxiliayField = auxiliayField;
    }

    public Integer getFlightTimes() {
        return flightTimes;
    }

    public void setFlightTimes(Integer flightTimes) {
        this.flightTimes = flightTimes;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
}