package com.juneyaoair.manage.b2c.entity.activity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 积分找回报名表
 *
 * @TableName T_RETRIEVAL_SIGN_UP_RECORD
 */
@Table(name = "T_RETRIEVAL_SIGN_UP_RECORD")
@Data
@SuppressWarnings("all")
public class RetrievalSignUpPO implements Serializable {
    /**
     * 会员卡号
     */
    @Column(name = "FFP_CARD_NO")
    private String ffpCardNo;

    /**
     * 报名时间
     */
    @Column(name = "SIGN_UP_TIME")
    private String signUpTime;

    /**
     * 失效积分
     */
    @Column(name = "INVALID_SCORES")
    private Integer invalidScores;

    /**
     * 飞行次数
     */
    @Column(name = "FLIGHT_TIMES")
    private Integer flightTimes;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;
}