<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.ChannelMapper">
    <delete id="delChannelType" parameterType="com.juneyaoair.manage.b2c.entity.ChannelPO">
        delete from tp_channel where pro_id = #{proId}
    </delete>

    <insert id="addChannel" parameterType="com.juneyaoair.manage.b2c.entity.ChannelPO">
        INSERT INTO tp_channel (ID,PRO_ID,CHANNEL_TYPE)values(#{id},#{proId},#{channelType})
    </insert>
    <insert id="addChannelType" parameterType="com.juneyaoair.manage.b2c.entity.ChannelPO">
        INSERT INTO tp_channel (ID,PRO_ID,CHANNEL_TYPE)values(#{id},#{proId},#{channelType})
    </insert>

</mapper>
