<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.CommonflycityPOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CommonflycityPO">
    <!--@mbg.generated-->
    <!--@Table T_COMMONFLYCITY-->
    <id column="CFC_ID" jdbcType="VARCHAR" property="cfcId" />
    <result column="RULE_NAME" jdbcType="VARCHAR" property="ruleName" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="UPDATE_MAN" jdbcType="VARCHAR" property="updateMan" />
    <result column="UPDATE_TIME" jdbcType="VARCHAR" property="updateTime" />
    <result column="CREATE_MAN" jdbcType="VARCHAR" property="createMan" />
    <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
    <result column="MIN_VER" jdbcType="VARCHAR" property="minVer" />
    <result column="MAX_VER" jdbcType="VARCHAR" property="maxVer" />
    <result column="PLATFORM_INFO" jdbcType="VARCHAR" property="platformInfo" />
    <result column="START_TIME" jdbcType="VARCHAR" property="startTime" />
    <result column="END_TIME" jdbcType="VARCHAR" property="endTime" />
    <result column="RULE_TYPE" jdbcType="VARCHAR" property="ruleType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CFC_ID, RULE_NAME, "STATUS", UPDATE_MAN, UPDATE_TIME, CREATE_MAN, CREATE_TIME, MIN_VER, 
    MAX_VER, PLATFORM_INFO, START_TIME, END_TIME, RULE_TYPE
  </sql>
</mapper>