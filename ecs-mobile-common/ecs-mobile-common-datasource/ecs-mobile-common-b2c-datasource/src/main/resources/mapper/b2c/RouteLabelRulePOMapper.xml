<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.RouteLabelRulePOMapper">
  <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.RouteLabelRulePO">
    <!--@mbg.generated-->
    <!--@Table T_ROUTE_LABEL_RULE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LABEL_ID" jdbcType="VARCHAR" property="labelId" />
    <result column="LABEL_RULE_NAME" jdbcType="VARCHAR" property="labelRuleName" />
    <result column="ROUTE_START_DATE" jdbcType="TIMESTAMP" property="routeStartDate" />
    <result column="ROUTE_END_DATE" jdbcType="TIMESTAMP" property="routeEndDate" />
    <result column="DEP_AIRPORT" jdbcType="VARCHAR" property="depAirport" />
    <result column="DEP_TERMINAL" jdbcType="VARCHAR" property="depTerminal" />
    <result column="DEP_COUNTRY" jdbcType="VARCHAR" property="depCountry" />
    <result column="DEP_REGION" jdbcType="VARCHAR" property="depRegion" />
    <result column="ARR_AIRPORT" jdbcType="VARCHAR" property="arrAirport" />
    <result column="ARR_TERMINAL" jdbcType="VARCHAR" property="arrTerminal" />
    <result column="ARR_COUNTRY" jdbcType="VARCHAR" property="arrCountry" />
    <result column="ARR_REGION" jdbcType="VARCHAR" property="arrRegion" />
    <result column="CARRIER" jdbcType="VARCHAR" property="carrier" />
    <result column="IS_TRANSIT" jdbcType="VARCHAR" property="isTransit" />
    <result column="TRANS_AIRPORT" jdbcType="VARCHAR" property="transAirport" />
    <result column="TRANS_DEP_TERMINAL" jdbcType="VARCHAR" property="transDepTerminal" />
    <result column="TRANS_SAME_AIRPORT" jdbcType="VARCHAR" property="transSameAirport" />
    <result column="TRANS_TIME" jdbcType="VARCHAR" property="transTime" />
    <result column="TRANS_DATE_LIMIT" jdbcType="VARCHAR" property="transDateLimit" />
    <result column="TRANS_PRE_FLIGHT_DATE_LIMIT" jdbcType="DECIMAL" property="transPreFlightDateLimit" />
    <result column="TRANS_NEXT_FLIGHT_DATE_LIMIT" jdbcType="DECIMAL" property="transNextFlightDateLimit" />
    <result column="LABEL_FUNCTION" jdbcType="VARCHAR" property="labelFunction" />
    <result column="ENABLE_STATUS" jdbcType="VARCHAR" property="enableStatus" />
    <result column="SORT_NUM" jdbcType="DECIMAL" property="sortNum" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LABEL_ID, LABEL_RULE_NAME, ROUTE_START_DATE, ROUTE_END_DATE, DEP_AIRPORT, DEP_TERMINAL, 
    DEP_COUNTRY, DEP_REGION, ARR_AIRPORT, ARR_TERMINAL, ARR_COUNTRY, ARR_REGION, CARRIER, 
    IS_TRANSIT, TRANS_AIRPORT, TRANS_DEP_TERMINAL, TRANS_SAME_AIRPORT, TRANS_TIME, TRANS_DATE_LIMIT, 
    TRANS_PRE_FLIGHT_DATE_LIMIT, TRANS_NEXT_FLIGHT_DATE_LIMIT, LABEL_FUNCTION, ENABLE_STATUS, 
    SORT_NUM, REMARK, CREATED_BY, CREATED_TIME, UPDATED_BY, UPDATED_TIME
  </sql>

  <select id="selectByLabelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM T_ROUTE_LABEL_RULE
    WHERE LABEL_ID = #{id,jdbcType=VARCHAR}
  </select>
</mapper>