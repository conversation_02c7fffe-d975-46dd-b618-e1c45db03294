<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.b2c.mapper.TCityLabelInfoMapper">
    <resultMap id="BaseResultMap" type="com.juneyaoair.manage.b2c.entity.CityLabelInfoPO">
        <id column="CITY_LABEL_ID" jdbcType="DECIMAL" property="cityLabelId"/>
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode"/>
        <result column="CITY_LABEL_NAME" jdbcType="VARCHAR" property="cityLabelName"/>
        <result column="CITY_LABEL_URL" jdbcType="VARCHAR" property="cityLabelUrl"/>
        <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime"/>
        <result column="LABEL_INTRODUCE" jdbcType="VARCHAR" property="labelIntroduce"/>
    </resultMap>
    <resultMap id="BaseResultMapV2" type="com.juneyaoair.ecs.manage.dto.activity.common.CityLabelInfoPOO">
        <id column="CITY_LABEL_ID" jdbcType="DECIMAL" property="cityLabelId"/>
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode"/>
        <result column="CITY_LABEL_NAME" jdbcType="VARCHAR" property="cityLabelName"/>
        <result column="CITY_LABEL_URL" jdbcType="VARCHAR" property="cityLabelUrl"/>
        <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="CREATETIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LABEL_INTRODUCE" jdbcType="VARCHAR" property="labelIntroduce"/>
    </resultMap>
    <sql id="Base_Column_List">
        CITY_LABEL_ID, CITY_CODE, CITY_LABEL_NAME, CITY_LABEL_URL, UPDATETIME, UPDATE_USER,
        CREATE_USER, CREATETIME, LABEL_INTRODUCE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_LABEL_INFO
        where CITY_LABEL_ID = #{cityLabelId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
        delete
        from T_CITY_LABEL_INFO
        where CITY_LABEL_ID = #{cityLabelId,jdbcType=DECIMAL}
    </delete>
    <insert id="insert" parameterType="com.juneyaoair.manage.b2c.entity.CityLabelInfoPO">
        insert into T_CITY_LABEL_INFO (CITY_LABEL_ID, CITY_CODE, CITY_LABEL_NAME,
                                       CITY_LABEL_URL, UPDATETIME, UPDATE_USER,
                                       CREATE_USER, CREATETIME, LABEL_INTRODUCE)
        values (SEQ_CITY_LABEL_INFO.nextval, #{cityCode,jdbcType=VARCHAR}, #{cityLabelName,jdbcType=VARCHAR},
                #{cityLabelUrl,jdbcType=VARCHAR}, #{updatetime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR},
                #{createUser,jdbcType=VARCHAR}, #{createtime,jdbcType=TIMESTAMP}, #{labelIntroduce,jdbcType=VARCHAR})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.juneyaoair.manage.b2c.entity.CityLabelInfoPO">
        update T_CITY_LABEL_INFO
        <set>
            <if test="cityCode != null">
                CITY_CODE = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityLabelName != null">
                CITY_LABEL_NAME = #{cityLabelName,jdbcType=VARCHAR},
            </if>
            <if test="cityLabelUrl != null">
                CITY_LABEL_URL = #{cityLabelUrl,jdbcType=VARCHAR},
            </if>
            <if test="updatetime != null">
                UPDATETIME = #{updatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createtime != null">
                CREATETIME = #{createtime,jdbcType=TIMESTAMP},
            </if>
            <if test="labelIntroduce != null">
                LABEL_INTRODUCE = #{labelIntroduce,jdbcType=VARCHAR},
            </if>
        </set>
        where CITY_LABEL_ID = #{cityLabelId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.juneyaoair.manage.b2c.entity.CityLabelInfoPO">

        update T_CITY_LABEL_INFO
        set CITY_CODE       = #{cityCode,jdbcType=VARCHAR},
            CITY_LABEL_NAME = #{cityLabelName,jdbcType=VARCHAR},
            CITY_LABEL_URL  = #{cityLabelUrl,jdbcType=VARCHAR},
            UPDATETIME      = #{updatetime,jdbcType=TIMESTAMP},
            UPDATE_USER     = #{updateUser,jdbcType=VARCHAR},
            CREATE_USER     = #{createUser,jdbcType=VARCHAR},
            CREATETIME      = #{createtime,jdbcType=TIMESTAMP},
            LABEL_INTRODUCE = #{labelIntroduce,jdbcType=VARCHAR}
        where CITY_LABEL_ID = #{cityLabelId,jdbcType=DECIMAL}
    </update>
    <!--auto generated by MybatisCodeHelper on 2023-05-11-->
    <select id="selectByCityCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_LABEL_INFO
        where CITY_CODE = #{cityCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_LABEL_INFO
        <where>
            <if test="cityLabelId != null">
                and CITY_LABEL_ID = #{cityLabelId,jdbcType=DECIMAL}
            </if>
            <if test="cityCode != null">
                and CITY_CODE = #{cityCode,jdbcType=VARCHAR}
            </if>
            <if test="cityLabelName != null">
                and CITY_LABEL_NAME = #{cityLabelName,jdbcType=VARCHAR}
            </if>
            <if test="cityLabelUrl != null">
                and CITY_LABEL_URL = #{cityLabelUrl,jdbcType=VARCHAR}
            </if>
            <if test="updatetime != null">
                and UPDATETIME = #{updatetime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null">
                and UPDATE_USER = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null">
                and CREATE_USER = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createtime != null">
                and CREATETIME = #{createtime,jdbcType=TIMESTAMP}
            </if>
            <if test="labelIntroduce != null">
                and LABEL_INTRODUCE = #{labelIntroduce,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectAllRecords" resultMap="BaseResultMapV2">
        select
        <include refid="Base_Column_List"/>
        from T_CITY_LABEL_INFO
    </select>

    <insert id="insertSelective">
        INSERT INTO T_CITY_LABEL_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cityLabelId!=null"> CITY_LABEL_ID,</if>
            <if test="cityCode!=null"> CITY_CODE,</if>
            <if test="cityLabelName!=null"> CITY_LABEL_NAME,</if>
            <if test="cityLabelUrl!=null"> CITY_LABEL_URL,</if>
            <if test="updatetime!=null"> UPDATETIME,</if>
            <if test="updateUser!=null"> UPDATE_USER,</if>
            <if test="createUser!=null"> CREATE_USER,</if>
            <if test="createtime!=null"> CREATETIME,</if>
            <if test="labelIntroduce!=null"> LABEL_INTRODUCE</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cityLabelId!=null"> #{cityLabelId,jdbcType=DECIMAL},</if>
            <if test="cityCode!=null"> #{cityCode,jdbcType=VARCHAR},</if>
            <if test="cityLabelName!=null"> #{cityLabelName,jdbcType=VARCHAR},</if>
            <if test="cityLabelUrl!=null"> #{cityLabelUrl,jdbcType=VARCHAR},</if>
            <if test="updatetime!=null"> #{updatetime,jdbcType=TIMESTAMP},</if>
            <if test="updateUser!=null"> #{updateUser,jdbcType=VARCHAR},</if>
            <if test="createUser!=null"> #{createUser,jdbcType=VARCHAR},</if>
            <if test="createtime!=null"> #{createtime,jdbcType=TIMESTAMP},</if>
            <if test="labelIntroduce!=null"> #{labelIntroduce,jdbcType=VARCHAR}</if>
        </trim>
</insert>
<!--auto generated by MybatisCodeHelper on 2023-05-18-->
    <select id="selectByCityCodeIn" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from T_CITY_LABEL_INFO
        where CITY_CODE in
        <foreach item="item" index="index" collection="cityCodeCollection"
                    open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByCityCodeInAndCityLabelIdBetween" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from T_CITY_LABEL_INFO
        where CITY_CODE in
        <foreach item="item" index="index" collection="cityCodeCollection"
                    open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
 and CITY_LABEL_ID <![CDATA[>]]> #{minCityLabelId,jdbcType=DECIMAL}  and CITY_LABEL_ID <![CDATA[<]]> #{maxCityLabelId,jdbcType=DECIMAL}
    </select>
</mapper>