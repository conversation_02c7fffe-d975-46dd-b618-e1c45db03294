package com.juneyaoair.manage.cki.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Table(name = "UPG_AIRLINE_RULE")
public class UpgAirlineRule {

    /** 主键 */
    @Id
    @TableId
    @Column(name = "AIRLINE_RULE_ID")
    private String airlineRuleId;

    /** 航线规则名称 */
    @Column(name = "AIRLINE_RULE_NAME")
    private String airlineRuleName;

    /** 航线性质 ORDINARY:普通线 BUSINESS:热门商务线 TOURISM:热门旅游线 */
    @Column(name = "AIRLINE_NATURE")
    private String airlineNature;

    /** 航线分组类型 DISTANCE:航距 AREA:地域 CUSTOM:自定义 */
    @Column(name = "AIRLINE_GROUP_TYPE")
    private String airlineGroupType;

    /** 航线类型 D:国内 I:国际 */
    @Column(name = "INT_FLAG")
    private String intFlag;

    /** 航线分组 */
    @Column(name = "AIRLINE_GROUP")
    private String airlineGroup;

    /** 航线清单(SET<STRING>格式) */
    @Column(name = "AIRLINE_SET")
    private String airlineSet;

    /** 状态 Y：有效 N：无效 D:已删除 */
    @Column(name = "STATUS")
    private String status;

    /** 创建时间 */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /** 创建人 */
    @Column(name = "CREATE_USER")
    private String createUser;

    /** 更新时间 */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /** 更新人 */
    @Column(name = "UPDATE_USER")
    private String updateUser;

}