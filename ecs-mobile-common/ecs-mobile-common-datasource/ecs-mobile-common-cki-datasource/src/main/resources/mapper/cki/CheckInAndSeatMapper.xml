<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.cki.mapper.CheckInAndSeatMapper">

    <select id="getSidList" parameterType="com.juneyaoair.ecs.seat.param.SeatSidQuery" resultType="com.juneyaoair.ecs.seat.result.SeatSidInfo">
         SELECT SID "sid",
                TICKET_NO "ticketNo",
                CARD_NO "cardNo",
                to_char(TRANSACT_TIME,'yyyy-mm-dd hh24:mi:ss') "transactTime",
                STATUS "status",
                CASE STATUS when 'SELECT' THEN '选座成功' WHEN 'CHECKIN' THEN '值机成功' ELSE STATUS END "statusName"
           FROM T_CHECK_IN_AND_SEAT
          WHERE TRANSACT_TIME <![CDATA[ >= ]]> #{dateStart}
            AND TRANSACT_TIME <![CDATA[ <= ]]> #{dateEnd}
            <if test="sid != null and sid != ''">
            AND SID like #{sid} || '%'
            </if>
            <if test="status != null and status != ''">
            AND STATUS = #{status}
            </if>
       ORDER BY TRANSACT_TIME DESC
    </select>

</mapper>