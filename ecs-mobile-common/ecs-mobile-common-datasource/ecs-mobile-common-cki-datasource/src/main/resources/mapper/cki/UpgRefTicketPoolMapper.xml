<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juneyaoair.manage.cki.mapper.UpgRefTicketPoolMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        insert into UPG_REF_TICKET_POOL (
            ID,
            TICKET_TYPE,
            TICKET_NO,
            STATUS,
            REF_SET_ID,
            FLIGHT_TYPE,
            CREATE_TIME
        )
        select SEQ_UPG_REF_TICKET_POOL.Nextval, a.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
                #{item.ticketType, jdbcType=VARCHAR},
                #{item.ticketNo, jdbcType=VARCHAR},
                #{item.status, jdbcType=VARCHAR},
                #{item.refSetId, jdbcType=VARCHAR},
                #{item.flightType, jdbcType=VARCHAR},
                sysdate
            from dual
        </foreach>
        ) a
    </insert>

</mapper>
