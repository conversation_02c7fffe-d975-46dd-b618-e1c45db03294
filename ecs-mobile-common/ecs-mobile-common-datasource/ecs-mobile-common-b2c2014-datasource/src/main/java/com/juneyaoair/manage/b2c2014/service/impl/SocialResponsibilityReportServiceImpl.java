package com.juneyaoair.manage.b2c2014.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.juneyaoair.manage.b2c2014.entity.SocialResponsibilityReportPO;
import com.juneyaoair.manage.b2c2014.mapper.SocialResponsibilityReportMapper;
import com.juneyaoair.manage.b2c2014.service.SocialResponsibilityReportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【T_ADVERTISEMENT】的数据库操作Service实现
* @createDate 2024-06-13 08:49:05
*/
@Service
public class SocialResponsibilityReportServiceImpl extends ServiceImpl<SocialResponsibilityReportMapper, SocialResponsibilityReportPO>
    implements SocialResponsibilityReportService {

    @Resource
    private SocialResponsibilityReportMapper socialResponsibilityReportMapper;


    @Override
    public List<SocialResponsibilityReportPO> toCatchAllByCondition(SocialResponsibilityReportPO socialResponsibilityReports) {
        return socialResponsibilityReportMapper.searchAllByCondition(socialResponsibilityReports);
    }
}




