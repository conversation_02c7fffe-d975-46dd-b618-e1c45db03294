package com.juneyaoair.manage.b2c2014.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.juneyaoair.manage.b2c2014.entity.NewsCategoryPO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【T_NEWS_CATEGORY(新闻分类)】的数据库操作Service
 * @createDate 2024-03-12 10:27:50
 */
public interface NewsCategoryService extends IService<NewsCategoryPO> {


    /**
     * @param newsCategoryPO
     * @return java.util.List<com.juneyaoair.manage.b2c2014.entity.NewsCategoryPO>
     * <AUTHOR>
     * @Description 筛选符合条件的栏目组记录
     * @Date 11:04 2024/3/12
     **/

    List<NewsCategoryPO> toSearchAllRecords(NewsCategoryPO newsCategoryPO);

    /**
     * @param newsCategoryPO
     * @return int
     * <AUTHOR>
     * @Description 更新栏目
     * @Date 13:20 2024/3/12
     **/

    int toUpdateCategory(NewsCategoryPO newsCategoryPO);


}
