package com.juneyaoair.ecs.manage.service.passport.impl;

import com.juneyaoair.ecs.manage.dto.activity.request.passport.PassportRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.passport.PassportInformation;
import com.juneyaoair.ecs.manage.service.passport.IPassportService;
import com.juneyaoair.ecs.utils.DateUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.service.ActivityPrizeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName PassportServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/4/2 20:52
 * @Version 1.0
 */
@Service
@RefreshScope
@Slf4j
public class PassportServiceImpl implements IPassportService {


    @Autowired
    private ActivityPrizeRecordService activityPrizeRecordService;

    @Override
    public List<PassportInformation> toCatchPassportList(PassportRequest passportRequest) {
        //查询时间间隔是否正确 是否小于0或者大于3个月
        int receiveDiffDays = DateUtil.dateDiff(passportRequest.getReceiveStartTime(), passportRequest.getReceiveEndTime(), DateUtil.DATE_FORMATE);
        if (receiveDiffDays <= 0) {
            throw new HoServiceException("查询时间范围非法");
        }
        if (receiveDiffDays > 90) {
            throw new HoServiceException("查询时间范围间隔过大(已超过90个自然日),请缩小查询范围.");
        }
        return activityPrizeRecordService.toCatchActivityPrizeRecords(passportRequest);
    }
}
