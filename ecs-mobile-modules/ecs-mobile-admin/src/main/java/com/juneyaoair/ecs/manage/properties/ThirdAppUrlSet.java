package com.juneyaoair.ecs.manage.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 第三方地址集合
 * @date 2023/5/8 23:44
 */
@Configuration
@RefreshScope
@Data
public class ThirdAppUrlSet {
    /**
     * 航班任务调用地址
     */
    @Value("${thirdAppUrl.b2cJobUrl:https://b2cjob.juneyaoair.com}")
    private String b2cJobUrl;
    /**
     * 基础服务consumer调用地址
     */
    @Value("${thirdAppUrl.flightbasicUrl:}")
    private String flightbasicUrl;
    /**
     * 基础服务provider调用地址
     */
    @Value("${thirdAppUrl.flightbasicProviderUrl:}")
    private String flightbasicProviderUrl;
    /**
     * 基础服务activity调用地址
     */
    @Value("${thirdAppUrl.flightbasicActivityUrl:}")
    private String flightbasicActivityUrl;

    /** 值机选座、升舱服务 */
    @Value("${thirdAppUrl.cussUrl:}")
    private String cussUrl;

    /** openApi服务 */
    @Value("${thirdAppUrl.openApiUrl:}")
    private String openApiUrl;

    /** webUrl */
    @Value("${thirdAppUrl.webUrl:https://m.juneyaoair.com/server}")
    private String webUrl;

}
