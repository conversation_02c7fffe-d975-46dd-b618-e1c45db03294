package com.juneyaoair.ecs.manage.service.appversion.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.juneyaoair.ecs.manage.dto.appversion.AppVersionInfoDTO;
import com.juneyaoair.ecs.manage.enums.YorNEnum;
import com.juneyaoair.ecs.manage.mapstruct.AppVersionInfoPOMapStruct;
import com.juneyaoair.ecs.manage.service.appversion.IAppVersionManageAggrService;
import com.juneyaoair.ecs.redis.service.PrimaryRedisService;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.ecs.utils.SystemConstants;
import com.juneyaoair.ecs.utils.VersionNoUtil;
import com.juneyaoair.manage.b2c.entity.AppVersionInfoPO;
import com.juneyaoair.manage.b2c.entity.VersionInfoPO;
import com.juneyaoair.manage.b2c.mapper.AppVersionInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TVersionInfoMapper;
import com.juneyaoair.manage.b2c.service.IAppVersionInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AppVersionManageAggrServiceImpl implements IAppVersionManageAggrService {

    @Resource
    IAppVersionInfoService appVersionInfoService;
    @Resource
    TVersionInfoMapper tVersionInfoMapper;
    @Resource
    AppVersionInfoMapper appVersionInfoMapper;
    @Resource
    PrimaryRedisService primaryRedisService;

    @Override
//    @DSTransactional(rollbackFor = Exception.class)
    public int insertAppVersion(AppVersionInfoPO appVersionManage) {
//        因为表联合主键是INNER_ID、APP_VERSION_NO、DEVICE_TYPE，所以直接插入即可
//        List<AppVersionInfoPO> appVersionInfoPOS = appVersionInfoService.checkAppVersion(appVersionManage);
//        if (CollUtil.isNotEmpty(appVersionInfoPOS)) {
//            return -5;
//        }
        return appVersionInfoService.addAppVersion(appVersionManage);
    }

    @Override
    public int deleteAppVersion(AppVersionInfoPO appVersionManage) {
        return appVersionInfoService.deleteAppVersion(appVersionManage);
    }

    @Override
    @DSTransactional
    public boolean releaseAppVersion(AppVersionInfoPO appVersionManage) {
        LambdaUpdateWrapper<AppVersionInfoPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AppVersionInfoPO::getInnerId, appVersionManage.getInnerId())
                .eq(AppVersionInfoPO::getDeviceType, appVersionManage.getDeviceType())
                .set(appVersionManage.getAppVersionNo() != null, AppVersionInfoPO::getAppVersionNo,
                        appVersionManage.getAppVersionNo())
                .set(appVersionManage.getStatus() != null, AppVersionInfoPO::getStatus, appVersionManage.getStatus())
                .set(appVersionManage.getUpdateDetails() != null, AppVersionInfoPO::getUpdateDetails, appVersionManage.getUpdateDetails())
                .set(appVersionManage.getForceUpdate() != null, AppVersionInfoPO::getForceUpdate, appVersionManage.getForceUpdate());
        boolean update = appVersionInfoService.update(null, updateWrapper);
        if (YorNEnum.Y.getStr().equalsIgnoreCase(appVersionManage.getStatus())) {
            updateAppVersionCache(appVersionManage);
        }

        //查询当前更新的内置ID和修改内容
        AppVersionInfoPO appVersionManage1 = queryReleaseAppVersion(appVersionManage);
        if (appVersionManage1.getDeviceType().equalsIgnoreCase("android")) {
            updateAndroidVersionManage(appVersionManage1);
        } else {
            updateIosVersionManage(appVersionManage1);
        }


        return update;
    }

    @Override
    public AppVersionInfoPO queryReleaseAppVersion(AppVersionInfoPO appVersionManage) {
        LambdaQueryWrapper<AppVersionInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(appVersionManage.getInnerId()), AppVersionInfoPO::getInnerId, appVersionManage.getInnerId())
                .eq(StrUtil.isNotBlank(appVersionManage.getDeviceType()), AppVersionInfoPO::getDeviceType, appVersionManage.getDeviceType())
                .like(StrUtil.isNotBlank(appVersionManage.getAppVersionNo()), AppVersionInfoPO::getAppVersionNo, appVersionManage.getAppVersionNo())
                .eq(StrUtil.isNotBlank(appVersionManage.getStatus()), AppVersionInfoPO::getStatus, appVersionManage.getStatus())
                .eq(StrUtil.isNotBlank(appVersionManage.getForceUpdate()), AppVersionInfoPO::getForceUpdate, appVersionManage.getForceUpdate());
        return appVersionInfoService.getOne(queryWrapper);
    }

    @Override
    public int updateAndroidVersionManage(AppVersionInfoPO appVersionManage1) {
        LambdaUpdateWrapper<VersionInfoPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(VersionInfoPO::getVersionType, "ANDROID_VERCODE")
                .set(appVersionManage1.getInnerId() != null, VersionInfoPO::getVersionNo, appVersionManage1.getInnerId())
                .set(StrUtil.isNotBlank(appVersionManage1.getUpdateDetails()), VersionInfoPO::getUpdateDetails, appVersionManage1.getUpdateDetails());
        return tVersionInfoMapper.update(null, wrapper);
    }

    @Override
    public int updateIosVersionManage(AppVersionInfoPO appVersionManage1) {
        LambdaUpdateWrapper<VersionInfoPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(VersionInfoPO::getVersionType, "IOS_VERCODE")
                .set(appVersionManage1.getInnerId() != null, VersionInfoPO::getVersionNo, appVersionManage1.getInnerId())
                .set(StrUtil.isNotBlank(appVersionManage1.getUpdateDetails()), VersionInfoPO::getUpdateDetails, appVersionManage1.getUpdateDetails());
        return tVersionInfoMapper.update(null, wrapper);
    }

    @Override
    public int offlineAppVersion(AppVersionInfoPO appVersionManage) {
        return appVersionInfoMapper.updateByInnerIdAndDeviceType(appVersionManage,
                appVersionManage.getInnerId(),
                appVersionManage.getDeviceType());
    }

    @Override
    public int forceupdateAppVersion(AppVersionInfoPO appVersionManage) {
        return appVersionInfoMapper.updateByInnerIdAndDeviceType(appVersionManage,
                appVersionManage.getInnerId(),
                appVersionManage.getDeviceType());
    }

    private void updateAppVersionCache(AppVersionInfoPO appVersionManage) {
        LambdaQueryWrapper<AppVersionInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(appVersionManage.getInnerId()), AppVersionInfoPO::getInnerId, appVersionManage.getInnerId())
                .eq(StrUtil.isNotBlank(appVersionManage.getDeviceType()), AppVersionInfoPO::getDeviceType, appVersionManage.getDeviceType())
                .eq(StrUtil.isNotBlank(appVersionManage.getAppVersionNo()), AppVersionInfoPO::getAppVersionNo, appVersionManage.getAppVersionNo())
                .eq(StrUtil.isNotBlank(appVersionManage.getStatus()), AppVersionInfoPO::getStatus, appVersionManage.getStatus())
                .eq(StrUtil.isNotBlank(appVersionManage.getForceUpdate()), AppVersionInfoPO::getForceUpdate, appVersionManage.getForceUpdate());

        AppVersionInfoPO appVersionManageList = appVersionInfoService.getOne(queryWrapper);
        if (appVersionManageList == null) {
            return;
        }
        primaryRedisService.put(SystemConstants.REDIS_APP_RELEASE_VER, JsonUtil.objectToJson(appVersionManageList), 60 * 60 * 24 * 30L/*30 days*/);
    }

    @Override
    public int modifyAppManage(AppVersionInfoPO appVersionManage) {
        return appVersionInfoMapper.updateByInnerIdAndDeviceType(appVersionManage,
                appVersionManage.getInnerId(),
                appVersionManage.getDeviceType());
    }

    @Override
    public List<AppVersionInfoDTO> list(AppVersionInfoPO appVersionManage) {
        List<AppVersionInfoPO> appVersionInfoPOList = appVersionInfoMapper.queryByAll(appVersionManage);
        List<AppVersionInfoDTO> appVersionInfoDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(appVersionInfoPOList)){
            for(AppVersionInfoPO appVersionInfoPO:appVersionInfoPOList){
                AppVersionInfoDTO appVersionInfoDTO = AppVersionInfoPOMapStruct.INSTANCE.toAppVersionInfoDTO(appVersionInfoPO);
                int sort = 0;
                try{
                    sort = VersionNoUtil.toVerInt(appVersionInfoDTO.getAppVersionNo());
                }catch (Exception e){
                }
                appVersionInfoDTO.setInnerIdSort(sort);
                appVersionInfoDTOList.add(appVersionInfoDTO);
            }
            appVersionInfoDTOList = appVersionInfoDTOList.stream().sorted(Comparator.comparing(AppVersionInfoDTO::getInnerIdSort).reversed()).collect(Collectors.toList());
        }
        return appVersionInfoDTOList;
    }
}
