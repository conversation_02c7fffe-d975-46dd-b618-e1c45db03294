package com.juneyaoair.ecs.manage.controller.appManage;

import com.juneyaoair.ecs.manage.common.AbsOrdinaryProcessor;
import com.juneyaoair.ecs.manage.common.AbsPageProcessor;
import com.juneyaoair.ecs.manage.dto.appversion.AppVersionInfoDTO;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.appversion.IAppVersionManageAggrService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.AppVersionInfoPO;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "AppVersionManageController", tags = "app版本管理")
@RequestMapping("appVersionManage")
@RestController
public class AppVersionManageController {

    @Resource
    private IAppVersionManageAggrService appVersionManageService;

    /**
     * 添加版本信息
     *
     * @param appVersionManage
     * @return
     */
    @PostMapping(value = "addAppVersion", produces = "application/json; charset=utf-8")
    public R<Boolean> addVersion(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, Boolean>(appVersionManage) {
            @Override
            public Boolean process() {
                if(StringUtils.isBlank(appVersionManage.getDeviceType())){
                    throw new HoServiceException("请输入应用平台信息");
                }
                appVersionManage.setStatus("N");
                appVersionManage.setForceUpdate("N");
                int flag = appVersionManageService.insertAppVersion(appVersionManage);
                if (flag == 0) {
                    throw new HoServiceException("版本信息已存在！");
                }
                return true;
            }
        }.execute();
    }

    /**
     * 删除版本信息
     *
     * @param appVersionManage
     * @return
     */
    @PostMapping(value = "deleteAppVersion", produces = "application/json; charset=utf-8")
    public R<Boolean> deleteAppVersion(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, Boolean>(appVersionManage) {
            @Override
            public Boolean process() {
                if (appVersionManage.getInnerId() == null) {
                    throw new HoServiceException("InnerId未指定！");
                }
                return appVersionManageService.deleteAppVersion(appVersionManage) > 0;
            }
        }.execute();
    }

    /**
     * 发布版本信息
     *
     * @param appVersionManage
     * @return
     */
    @PostMapping(value = "releaseAppVersion", produces = "application/json; charset=utf-8")
    public R<Boolean> releaseAppVersion(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, Boolean>(appVersionManage) {
            @Override
            public Boolean process() {
                if (appVersionManage.getInnerId() == null) {
                    throw new HoServiceException("InnerId未指定！");
                }
                int result = 0;
                appVersionManage.setStatus("Y");
                return appVersionManageService.releaseAppVersion(appVersionManage);
            }
        }.execute();
    }

    /**
     * 下线版本信息
     *
     * @param appVersionManage
     * @return
     */
    @PostMapping(value = "offlineAppVersion", produces = "application/json; charset=utf-8")
    public R<Boolean> offlineAppVersion(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, Boolean>(appVersionManage) {
            @Override
            public Boolean process() {
                if (appVersionManage.getInnerId() == null) {
                    throw new HoServiceException("InnerId未指定！");
                }
                int result = 0;
                appVersionManage.setStatus("N");
                return appVersionManageService.offlineAppVersion(appVersionManage) > 0;
            }
        }.execute();
    }

    /**
     * 强制更新版本信息
     *
     * @param appVersionManage
     * @return
     */
    @PostMapping(value = "forceUpdateAppVersion", produces = "application/json; charset=utf-8")
    public Object forceUpdateAppVersion(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, Boolean>(appVersionManage) {
            @Override
            public Boolean process() {
                if (appVersionManage.getInnerId() == null) {
                    throw new HoServiceException("InnerId未指定！");
                }
                int result = 0;
                appVersionManage.setForceUpdate("Y");
                return appVersionManageService.forceupdateAppVersion(appVersionManage) > 0;

            }
        }.execute();

    }

    /**
     * 取消强制更新
     *
     * @param appVersionManage
     * @return
     */
    @PostMapping(value = "cancelForceUpdateAppVersion", produces = "application/json; charset=utf-8")
    public R<Boolean> cancelForceUpdateAppVersion(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, Boolean>(appVersionManage) {
            @Override
            public Boolean process() {
                if (appVersionManage.getInnerId() == null) {
                    throw new HoServiceException("InnerId未指定！");
                }
                appVersionManage.setForceUpdate("N");
                return appVersionManageService.forceupdateAppVersion(appVersionManage) > 0;
            }
        }.execute();

    }


    /**
     * 修改版本信息
     *
     * @param appVersionManage
     * @return
     */
    @PostMapping(value = "modifyAppManage", produces = "application/json; charset=utf-8")
    public R<Boolean> modifyAppManage(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, Boolean>(appVersionManage) {
            @Override
            public Boolean process() {
                if (appVersionManage.getInnerId().equalsIgnoreCase("")) {
                    throw new HoServiceException("内置ID不能为空");
                }
                if (appVersionManage.getAppVersionNo().equalsIgnoreCase("")) {
                    throw new HoServiceException("版本号不能为空");
                }
                return appVersionManageService.modifyAppManage(appVersionManage) > 0;
            }
        }.execute();
    }

    /**
     * 查询版本信息
     *
     */
    @PostMapping(value = "queryAppVersionList")
    public R<PageResult<AppVersionInfoDTO>> queryAppVersionList(@RequestBody AppVersionInfoPO po) {
        return new AbsPageProcessor<AppVersionInfoPO, AppVersionInfoDTO>(po) {
            @Override
            public List<AppVersionInfoDTO> process() {
                return appVersionManageService.list(po);
            }
        }.execute();
    }

    @PostMapping(value = "getAppManageByInnerId")
    public R<AppVersionInfoDTO> getAppManageByInnerId(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, AppVersionInfoDTO>(appVersionManage) {
            @Override
            public AppVersionInfoDTO process() {
                return appVersionManageService.list(appVersionManage).stream().findFirst().orElse(null);
            }
        }.execute();
    }

    @PostMapping(value = "getAppVersionByDeviceType")
    public R<List<AppVersionInfoDTO>> getAppVersionByDeviceType(@RequestBody AppVersionInfoPO appVersionManage) {
        return new AbsOrdinaryProcessor<AppVersionInfoPO, List<AppVersionInfoDTO>>(appVersionManage) {
            @Override
            public List<AppVersionInfoDTO> process() {
                return appVersionManageService.list(appVersionManage);
            }
        }.execute();
    }
}
