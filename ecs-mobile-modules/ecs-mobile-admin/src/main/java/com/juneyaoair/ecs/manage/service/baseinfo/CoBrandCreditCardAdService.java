package com.juneyaoair.ecs.manage.service.baseinfo;

import com.juneyaoair.ecs.manage.dto.ad.cobrandcreditcard.dto.*;

import java.util.List;

public interface CoBrandCreditCardAdService {
    List<CoBrandCreditCardAdDTO> list(ListCoBrandCreditCardAdRequestDTO request);

    void addCreditCardAD(AddOrUpdateAdRequestDTO request);

    void updateCreditCardAD(AddOrUpdateAdRequestDTO request);

    void publishCreditCardAD(CreditCardAdIdRequestDTO request);

    void deleteCreditCardAD(CreditCardAdIdRequestDTO request);

    void addDetail(AddOrUpdateAdDetailRequestDTO request);

    void updateDetail(AddOrUpdateAdDetailRequestDTO request);

    void deleteDetail(AddOrUpdateAdDetailRequestDTO id);
}
