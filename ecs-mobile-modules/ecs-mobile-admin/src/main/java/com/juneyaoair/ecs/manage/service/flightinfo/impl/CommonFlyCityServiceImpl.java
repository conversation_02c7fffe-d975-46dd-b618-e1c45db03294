package com.juneyaoair.ecs.manage.service.flightinfo.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.juneyaoair.ecs.manage.dto.cfc.dto.*;
import com.juneyaoair.ecs.manage.service.flightinfo.CommonFlyCityService;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.entity.CommonflycityPO;
import com.juneyaoair.manage.b2c.entity.CommonflycitydetailPO;
import com.juneyaoair.manage.b2c.mapper.CommonflycityPOMapper;
import com.juneyaoair.manage.b2c.mapper.CommonflycitydetailPOMapper;
import com.juneyaoair.manage.b2c.mapper.TCityInfoMapper;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CommonFlyCityServiceImpl implements CommonFlyCityService {


    @Resource
    TCityInfoMapper cityInfoMapper;

    @Resource
    private CommonflycityPOMapper commonflycityPOMapper;

    @Resource
    private CommonflycitydetailPOMapper commonflycitydetailPOMapper;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public List<CommonFlyCityDTO> list(CommonFlyCityRequestDTO request) {
        LambdaQueryWrapper<CommonflycityPO> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(request.getRuleName())) {
            wrapper.like(CommonflycityPO::getRuleName, request.getRuleName());
        }
        if (StringUtils.isNotBlank(request.getRuleType())) {
            wrapper.eq(CommonflycityPO::getRuleType, request.getRuleType());
        }
        wrapper.orderByDesc(CommonflycityPO::getCreateTime);
        
        List<CommonflycityPO> poList = commonflycityPOMapper.selectList(wrapper);
        return poList.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    @DSTransactional
    public void add(AddOrUpdateCommonFlyCityRequestDTO request) {
        // 保存主表数据
        CommonflycityPO po = new CommonflycityPO();
        BeanUtils.copyProperties(request, po);
        po.setCreateTime(LocalDateTime.now().format(DATE_FORMATTER));
        po.setUpdateTime(LocalDateTime.now().format(DATE_FORMATTER));
        po.setCreateMan(SecurityUtils.getUsername());
        po.setUpdateMan(SecurityUtils.getUsername());
        commonflycityPOMapper.insert(po);

        // 保存详情表数据
        if (request.getCityDetails() != null) {
            for (CommonFlyCityDetailDTO detail : request.getCityDetails()) {
                // 查询城市信息
                CityInfoPO cityInfo = cityInfoMapper.selectById(detail.getCityCode());
                if (cityInfo == null) {
                    throw new RuntimeException("城市编码不存在：" + detail.getCityCode());
                }
                
                CommonflycitydetailPO detailPO = new CommonflycitydetailPO();
                detailPO.setCfcId(po.getCfcId());
                detailPO.setCityCode(cityInfo.getCityCode());
                detailPO.setCityName(cityInfo.getCityName());
                detailPO.setCityEName(cityInfo.getCityEName());

                detailPO.setCityPinYin(cityInfo.getCityPinYin());
                detailPO.setIsInternational(cityInfo.getIsInternational());
                detailPO.setCountryCode(cityInfo.getCountryCode());
                
                commonflycitydetailPOMapper.insert(detailPO);
            }
        }
    }

    @Override
    @DSTransactional
    public void update(AddOrUpdateCommonFlyCityRequestDTO request) {
        // 更新主表数据
        CommonflycityPO po = new CommonflycityPO();
        BeanUtils.copyProperties(request, po);
        po.setUpdateTime(LocalDateTime.now().format(DATE_FORMATTER));
        po.setUpdateMan(SecurityUtils.getUsername());
        commonflycityPOMapper.updateById(po);

        // 删除原有详情数据
        LambdaQueryWrapper<CommonflycitydetailPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommonflycitydetailPO::getCfcId, request.getCfcId());
        commonflycitydetailPOMapper.delete(wrapper);

        // 插入新的详情数据
        if (request.getCityDetails() != null) {
            for (CommonFlyCityDetailDTO detail : request.getCityDetails()) {
                // 查询城市信息
                CityInfoPO cityInfo = cityInfoMapper.selectById(detail.getCityCode());
                if (cityInfo == null) {
                    throw new RuntimeException("城市编码不存在：" + detail.getCityCode());
                }
                
                CommonflycitydetailPO detailPO = new CommonflycitydetailPO();
                detailPO.setCfcId(po.getCfcId());
                detailPO.setCityCode(cityInfo.getCityCode());
                detailPO.setCityName(cityInfo.getCityName());
                detailPO.setCityEName(cityInfo.getCityEName());
                detailPO.setCityPinYin(cityInfo.getCityPinYin());
                detailPO.setIsInternational(cityInfo.getIsInternational());
                detailPO.setCountryCode(cityInfo.getCountryCode());
                
                commonflycitydetailPOMapper.insert(detailPO);
            }
        }
    }

    @Override
    public void updateStatus(String cfcId, String status) {
        CommonflycityPO po = new CommonflycityPO();
        po.setCfcId(cfcId);
        po.setStatus(status);
        po.setUpdateTime(LocalDateTime.now().format(DATE_FORMATTER));
        po.setUpdateMan(SecurityUtils.getUsername());
        commonflycityPOMapper.updateById(po);
    }

    @Override
    @DSTransactional
    public void delete(String cfcId) {
        // 先删除详情表数据
        LambdaQueryWrapper<CommonflycitydetailPO> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(CommonflycitydetailPO::getCfcId, cfcId);
        commonflycitydetailPOMapper.delete(detailWrapper);
        
        // 再删除主表数据
        commonflycityPOMapper.deleteById(cfcId);
    }

    private CommonFlyCityDTO convertToDTO(CommonflycityPO po) {
        CommonFlyCityDTO dto = new CommonFlyCityDTO();
        BeanUtils.copyProperties(po, dto);
        
        // 查询并设置详情数据
        LambdaQueryWrapper<CommonflycitydetailPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommonflycitydetailPO::getCfcId, po.getCfcId());
        List<CommonflycitydetailPO> details = commonflycitydetailPOMapper.selectList(wrapper);
        
        List<CommonFlyCityDetailDTO> detailDTOs = details.stream().map(detail -> {
            CommonFlyCityDetailDTO detailDTO = new CommonFlyCityDetailDTO();
            BeanUtils.copyProperties(detail, detailDTO);
            return detailDTO;
        }).collect(Collectors.toList());
        
        dto.setCityDetail(detailDTOs);
        return dto;
    }
}
