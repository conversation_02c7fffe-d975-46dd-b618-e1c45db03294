package com.juneyaoair.ecs.seat.service;

import com.juneyaoair.ecs.seat.param.SeatCheckInQuery;
import com.juneyaoair.ecs.seat.param.SeatSelectQuery;
import com.juneyaoair.ecs.seat.param.SeatSidQuery;
import com.juneyaoair.ecs.seat.result.SeatCheckInInfo;
import com.juneyaoair.ecs.seat.result.SeatSelectSeatInfo;
import com.juneyaoair.ecs.seat.result.SeatSidInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 值机选座-报表
 * @created 2024/4/3 9:11
 */
public interface SeatReportService {

    /**
     * 值机选座-SID统计数据
     * @param seatSidQuery
     * @return
     */
    List<SeatSidInfo> getSidList(SeatSidQuery seatSidQuery);

    /**
     * 值机选座-选座统计数据
     * @param seatSelectQuery
     * @return
     */
    List<SeatSelectSeatInfo> querySelectSeatList(SeatSelectQuery seatSelectQuery);

    /**
     * 值机选座-值机统计数据
     * @param seatCheckInQuery
     * @return
     */
    List<SeatCheckInInfo> queryCheckInSeatList(SeatCheckInQuery seatCheckInQuery);
}
