package com.juneyaoair.ecs.manage.service.airline;

import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AddOrUpDateAirlineClassDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AirlineClassDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.AirlineClassIdRequestDTO;
import com.juneyaoair.ecs.manage.dto.airline.airlineclass.QueryAirlineClassRequestDTO;

import java.util.List;

public interface IAirlineClassService {
    List<AirlineClassDTO> list(QueryAirlineClassRequestDTO request);

    void add(AddOrUpDateAirlineClassDTO request);

    void update(AddOrUpDateAirlineClassDTO request);

    void delete(AirlineClassIdRequestDTO request);

    Object logList(AirlineClassIdRequestDTO request);
}
