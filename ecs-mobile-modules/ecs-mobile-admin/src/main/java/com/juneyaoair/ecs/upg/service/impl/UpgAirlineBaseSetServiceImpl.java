package com.juneyaoair.ecs.upg.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.juneyaoair.ecs.manage.constant.ManageConstant;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.external.CussService;
import com.juneyaoair.ecs.manage.external.FlightBasicService;
import com.juneyaoair.ecs.manage.external.flightbaisc.AirlineMileageInfo;
import com.juneyaoair.ecs.redis.service.PrimaryRedisService;
import com.juneyaoair.ecs.redis.service.RedisKeyConstants;
import com.juneyaoair.ecs.upg.constant.OperateEnum;
import com.juneyaoair.ecs.upg.service.UpgLogService;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.entity.AirportInfoPO;
import com.juneyaoair.manage.b2c.entity.CityInfoPO;
import com.juneyaoair.manage.b2c.entity.CountryPO;
import com.juneyaoair.manage.b2c.mapper.TAirlineAMapper;
import com.juneyaoair.manage.b2c.mapper.TAirportInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TCityInfoMapper;
import com.juneyaoair.manage.b2c.mapper.TCountryMapper;
import com.juneyaoair.manage.cki.entity.UpgAirlineBaseSet;
import com.juneyaoair.manage.cki.mapper.UpgAirlineBaseSetMapper;
import com.juneyaoair.ecs.upg.param.CreateAirlineBaseSetParam;
import com.juneyaoair.ecs.upg.param.QueryAirlineBaseSetParam;
import com.juneyaoair.ecs.upg.param.UpdateAirlineBaseSetParam;
import com.juneyaoair.ecs.upg.result.UpgAirlineBaseSetInfo;
import com.juneyaoair.ecs.upg.service.UpgAirlineBaseSetService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 升舱航线分组管理
 * @created 2023/8/16 9:23
 */
@Service
public class UpgAirlineBaseSetServiceImpl implements UpgAirlineBaseSetService {

    @Resource
    private PrimaryRedisService primaryRedisService;
    @Autowired
    private FlightBasicService flightBasicService;

    @Autowired
    private CussService cussService;

    @Autowired
    private UpgLogService upgLogService;

    @Resource
    private TAirportInfoMapper airportInfoMapper;
    @Resource
    private TCityInfoMapper cityInfoMapper;
    @Resource
    private TCountryMapper countryMapper;
    @Resource
    private TAirlineAMapper airLineMapper;
    @Resource
    private UpgAirlineBaseSetMapper upgAirlineBaseSetMapper;

    @Override
    @DSTransactional
    public void createAirlineBaseSet(CreateAirlineBaseSetParam createAirlineBaseSetParam) {
        if (!"I".equals(createAirlineBaseSetParam.getIntFlag())) {
            throw new HoServiceException("仅支持国际航线录入");
        }
        if (!"area".equals(createAirlineBaseSetParam.getClassifyType())) {
            throw new HoServiceException("仅支持地域分组方式录入");
        }
        UpgAirlineBaseSet upgAirlineBaseSet = new UpgAirlineBaseSet();
        if ("D".equals(createAirlineBaseSetParam.getIntFlag())) {
            upgAirlineBaseSet.setAirlineName("国内航线");
        } else if ("I".equals(createAirlineBaseSetParam.getIntFlag())) {
            upgAirlineBaseSet.setAirlineName("国际航线");
        } else {
            throw new HoServiceException("航线类型名称不能为空");
        }
        upgAirlineBaseSet.setId(UUID.randomUUID().toString());
        upgAirlineBaseSet.setIntFlag(createAirlineBaseSetParam.getIntFlag());
        upgAirlineBaseSet.setClassifyType(createAirlineBaseSetParam.getClassifyType());
        upgAirlineBaseSet.setClassifyValue(createAirlineBaseSetParam.getClassifyValue());
        upgAirlineBaseSet.setAirlineDesc(createAirlineBaseSetParam.getAirlineDesc());
        upgAirlineBaseSet.setStatus("Y");
        String username = SecurityUtils.getUsername();
        upgAirlineBaseSet.setCreateUser(username);
        upgAirlineBaseSet.setLastUpdateUser(username);
        upgAirlineBaseSet.setCreateTime(new Date());
        upgAirlineBaseSet.setLastUpdateTime(new Date());
        upgAirlineBaseSetMapper.insert(upgAirlineBaseSet);
        // 增加操作日志
        upgLogService.save("airlineBaseSet", upgAirlineBaseSet.getId(), OperateEnum.ADD.getCode(), upgAirlineBaseSet);
        primaryRedisService.removeAllKey(RedisKeyConstants.REDIS_UPG_SEGMENT + "*");
        // 更新升舱系统航线缓存数据
        cussService.refreshAirlineBaseSetRedis();
    }

    @Override
    @DSTransactional
    public void updateAirlineBaseSet(UpdateAirlineBaseSetParam updateAirlineBaseSetParam) {
        if (StringUtils.isAllBlank(updateAirlineBaseSetParam.getAirlineDesc(), updateAirlineBaseSetParam.getAirlineDesc())) {
            throw new HoServiceException("参数校验不通过");
        }
        UpgAirlineBaseSet upgAirlineBaseSetDb = upgAirlineBaseSetMapper.selectById(updateAirlineBaseSetParam.getId());
        if (null == upgAirlineBaseSetDb) {
            throw new HoServiceException("数据不存在，更新失败");
        }
        if ("D".equals(upgAirlineBaseSetDb.getStatus())) {
            throw new HoServiceException("数据已删除，更新失败");
        }
        if ("D".equals(upgAirlineBaseSetDb.getIntFlag()) || !"area".equals(upgAirlineBaseSetDb.getClassifyType())) {
            throw new HoServiceException("该类型数据不支持修改，更新失败");
        }
        UpgAirlineBaseSet upgAirlineBaseSet = new UpgAirlineBaseSet();
        if (StringUtils.isNotBlank(updateAirlineBaseSetParam.getAirlineDesc())) {
            upgAirlineBaseSet.setAirlineDesc(updateAirlineBaseSetParam.getAirlineDesc());
        }
        if (StringUtils.isNotBlank(updateAirlineBaseSetParam.getClassifyValue())) {
            upgAirlineBaseSet.setClassifyValue(updateAirlineBaseSetParam.getClassifyValue());
        }
        upgAirlineBaseSet.setLastUpdateTime(new Date());
        upgAirlineBaseSet.setLastUpdateUser(SecurityUtils.getUsername());
        QueryWrapper<UpgAirlineBaseSet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", updateAirlineBaseSetParam.getId());
        int count = upgAirlineBaseSetMapper.update(upgAirlineBaseSet, queryWrapper);
        if (count <= 0) {
            throw new HoServiceException("更新失败");
        }
        // 增加操作日志
        upgLogService.save("airlineBaseSet", updateAirlineBaseSetParam.getId(), OperateEnum.UPDATE.getCode(), updateAirlineBaseSetParam);
        primaryRedisService.removeAllKey(RedisKeyConstants.REDIS_UPG_SEGMENT + "*");
        // 更新升舱系统航线缓存数据
        cussService.refreshAirlineBaseSetRedis();
    }

    @Override
    @DSTransactional
    public void deleteAirlineBaseSet(String id) {
        UpgAirlineBaseSet upgAirlineBaseSet = new UpgAirlineBaseSet();
        upgAirlineBaseSet.setStatus("D");
        upgAirlineBaseSet.setLastUpdateTime(new Date());
        upgAirlineBaseSet.setLastUpdateUser(SecurityUtils.getUsername());
        QueryWrapper<UpgAirlineBaseSet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", id);
        int count = upgAirlineBaseSetMapper.update(upgAirlineBaseSet, queryWrapper);
        if (count <= 0) {
            throw new HoServiceException("航线分组删除失败");
        }
        // 增加操作日志
        upgLogService.save("airlineBaseSet", id, OperateEnum.DEL.getCode(), id);
        primaryRedisService.removeAllKey(RedisKeyConstants.REDIS_UPG_SEGMENT + "*");
        // 更新升舱系统航线缓存数据
        cussService.refreshAirlineBaseSetRedis();
    }

    @Override
    public PageResult<UpgAirlineBaseSetInfo> queryAirlineBaseSet(QueryAirlineBaseSetParam queryAirlineBaseSetParam) {
        final String redisKey = RedisKeyConstants.REDIS_UPG_SEGMENT + queryAirlineBaseSetParam.getPageNum() + ":" + queryAirlineBaseSetParam.getPageSize() + ":"
                                + (StringUtils.isBlank(queryAirlineBaseSetParam.getAirlineCode()) ? "ALL" : queryAirlineBaseSetParam.getAirlineCode()) + ":"
                                + (StringUtils.isBlank(queryAirlineBaseSetParam.getAirlineDesc()) ? "" : queryAirlineBaseSetParam.getAirlineDesc());
        TypeReference<PageResult<UpgAirlineBaseSetInfo>> typeReference = new TypeReference<PageResult<UpgAirlineBaseSetInfo>>() { };
        String redisValue = primaryRedisService.get(redisKey);
        if (StringUtils.isNotBlank(redisValue)) {
            return JSON.parseObject(redisValue, typeReference);
        }
        PageHelper.startPage(queryAirlineBaseSetParam.getPageNum(), queryAirlineBaseSetParam.getPageSize());
        QueryWrapper<UpgAirlineBaseSet> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(queryAirlineBaseSetParam.getAirlineCode())) {
            queryWrapper.eq("INT_FLAG", queryAirlineBaseSetParam.getAirlineCode());
        }
        if (StringUtils.isNotEmpty(queryAirlineBaseSetParam.getAirlineDesc())) {
            queryWrapper.like("AIRLINE_DESC", queryAirlineBaseSetParam.getAirlineDesc());
        }
        queryWrapper.in("STATUS", Lists.newArrayList("Y", "N"));
        queryWrapper.orderByDesc("LAST_UPDATE_TIME");
        List<UpgAirlineBaseSet> upgAirlineBaseSetList = upgAirlineBaseSetMapper.selectList(queryWrapper);
        PageInfo<UpgAirlineBaseSet> pageInfo = new PageInfo<>(upgAirlineBaseSetList);
        PageResult<UpgAirlineBaseSetInfo> pageResult = new PageResult<>();
        pageResult.setPageNum(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        pageResult.setTotal(pageInfo.getTotal());
        if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            // 查询所有行距信息
            List<AirlineMileageInfo> airLineMileageList = flightBasicService.getAirLineMileage(null, null, null, null);
            //查询全部城市信息
            CityInfoPO cityInfo = new CityInfoPO();
            List<CityInfoPO> cityInfoList = cityInfoMapper.selectByAll(cityInfo);
            Map<String, CityInfoPO> cityMap = cityInfoList.stream().collect(Collectors.toMap(k -> k.cityCode, v -> v));
            // 查询全部机场信息
            AirportInfoPO airportInfo = new AirportInfoPO();
            airportInfo.setStatus("1");
            List<AirportInfoPO> airportInfoList = airportInfoMapper.selectByAll(airportInfo);
            // 查询全部航线
            List<AirlineAPO> airLineList = airLineMapper.selectByAll(null);
            // 按照城市转换机场对
            Map<String, Set<String>> cityAirportMap = Maps.newHashMap();
            for (AirportInfoPO airInfo : airportInfoList) {
                Set<String> airports = cityAirportMap.get(airInfo.getCityCode());
                airports = null == airports ? Sets.newHashSet() : airports;
                airports.add(airInfo.getAirportCode());
                cityAirportMap.put(airInfo.getCityCode(), airports);
            }
            List<UpgAirlineBaseSetInfo> upgAirlineBaseSetInfoList = Lists.newArrayList();
            for (UpgAirlineBaseSet upgAirlineBaseSet : pageInfo.getList()) {
                UpgAirlineBaseSetInfo upgAirlineBaseSetInfo = new UpgAirlineBaseSetInfo();
                BeanUtils.copyProperties(upgAirlineBaseSet, upgAirlineBaseSetInfo);
                List<String> segmentList = null;
                // 国内航线按航距分组
                if ("D".equals(upgAirlineBaseSet.getIntFlag())) {
                    // 空白航距
                    if ("100000-100000".equals(upgAirlineBaseSet.getClassifyValue())) {
                        segmentList = getNoSegments(airLineList, airLineMileageList, cityAirportMap);

                    } else {
                        segmentList = getCnAirlineInfo(upgAirlineBaseSet.getClassifyValue(), airLineMileageList, cityAirportMap);
                    }
                    upgAirlineBaseSetInfo.setCountryName(ManageConstant.CN_DESC);
                }
                // 国际航线按区域分组
                if ("I".equals(upgAirlineBaseSet.getIntFlag())) {
                    Set<String> countrySet = Sets.newHashSet(upgAirlineBaseSet.getClassifyValue().split(","));
                    segmentList = getIAirlineSegments(countrySet, airLineList, cityMap, cityAirportMap);
                    // 国家名称
                    QueryWrapper<CountryPO> countryQueryWrapper = new QueryWrapper<>();
                    countryQueryWrapper.in("COUNTRY_CODE", countrySet);
                    List<CountryPO> countryList = countryMapper.selectList(countryQueryWrapper);
                    Map<String, CountryPO> countryMap = countryList.stream().collect(Collectors.toMap(CountryPO::getCountryCode, x -> x));
                    List<String> countryNameList = Lists.newArrayList();
                    countrySet.forEach(countryCode -> {
                        CountryPO country = countryMap.get(countryCode);
                        if (null == country) {
                            countryNameList.add(countryCode);
                        } else {
                            countryNameList.add(country.getCountryName());
                        }
                    });
                    upgAirlineBaseSetInfo.setCountryName(Joiner.on(",").join(countryNameList));
                }
                upgAirlineBaseSetInfo.setSegments(segmentList);
                upgAirlineBaseSetInfoList.add(upgAirlineBaseSetInfo);
            }
            pageResult.setRows(upgAirlineBaseSetInfoList);
        }
        primaryRedisService.setJSON(redisKey, pageResult, 10 * 60L);
        return pageResult;
    }

    /**
     * 空白航距航线
     * @param airLineMileageList
     * @param cityAirportMap
     * @return
     */
    private List<String> getNoSegments(List<AirlineAPO> airLineList, List<AirlineMileageInfo> airLineMileageList, Map<String, Set<String>> cityAirportMap) {
        Set<String> mileageSegmentSet = Sets.newHashSet();
        airLineMileageList.forEach(airlineMileageInfo -> {
            if ("D".equals(airlineMileageInfo.getIsInternationalAirline())) {
                mileageSegmentSet.add(airlineMileageInfo.getDepCity() + "-" + airlineMileageInfo.getArrCity());
            }
        });
        Set<String> segmentSet = Sets.newHashSet();
        for (AirlineAPO airline : airLineList) {
            if (mileageSegmentSet.contains(airline.getDepCity() + "-" + airline.getArrCity())) {
                continue;
            }
            // 非国内航线处理下一个
            if (!"D".equals(airline.getIsInternationalAirline())) {
                continue;
            }
            Set<String> depAirports = cityAirportMap.get(airline.getDepCity());
            Set<String> arrAirports = cityAirportMap.get(airline.getArrCity());
            if (depAirports == null || arrAirports == null) {
                continue;
            }
            for (String dep : depAirports) {
                for (String arr : arrAirports) {
                    segmentSet.add(dep + "-" + arr);
                }
            }
        }
        List<String> segmentList = Lists.newArrayList(segmentSet);
        Collections.sort(segmentList);
        return segmentList;
    }

    /**
     * 符合国内航距数据的全部航线
     * @param classifyValue
     * @param airLineMileageList
     * @param cityAirportMap
     * @return
     */
    private List<String> getCnAirlineInfo(String classifyValue, List<AirlineMileageInfo> airLineMileageList, Map<String, Set<String>> cityAirportMap) {
        String[] split = classifyValue.split("-");
        int min = new Integer(split[0]);
        int max = new Integer(split[1]);
        Set<String> segmentSet = Sets.newHashSet();
        for (AirlineMileageInfo airlineMileage : airLineMileageList) {
            // 非国内航距不以航距分组
            if (!"D".equals(airlineMileage.getIsInternationalAirline())) {
                continue;
            }
            int mile = new Integer(airlineMileage.getMileage());
            if (mile < min || mile >= max) {
                continue;
            }
            Set<String> depAirports = cityAirportMap.get(airlineMileage.getDepCity());
            Set<String> arrAirports = cityAirportMap.get(airlineMileage.getArrCity());
            if (depAirports == null || arrAirports == null) {
                continue;
            }
            for (String dep : depAirports) {
                for (String arr : arrAirports) {
                    segmentSet.add(dep + "-" + arr);
                }
            }
        }
        List<String> segmentList = Lists.newArrayList(segmentSet);
        Collections.sort(segmentList);
        return segmentList;
    }

    /**
     * 国际航线
     * @param countrySet
     * @param airLineList
     * @param cityMap
     * @param cityAirportMap
     * @return
     */
    private List<String> getIAirlineSegments(Set<String> countrySet, List<AirlineAPO> airLineList, Map<String, CityInfoPO> cityMap, Map<String, Set<String>> cityAirportMap) {
        List<String> countryCityList = new ArrayList<>();
        for (CityInfoPO cityInfo : cityMap.values()) {
            // 国内航距不以区域分组
            if ("D".equals(cityInfo.getIsInternational())) {
                continue;
            }
            if (countrySet.contains(cityInfo.getCountryCode())) {
                countryCityList.add(cityInfo.getCityCode());
            }
        }
        if (CollectionUtils.isEmpty(countryCityList)) {
            return null;
        }
        Set<String> segmentSet = Sets.newHashSet();
        for (AirlineAPO airline : airLineList) {
            // 国内航距不以区域分组
            if ("D".equals(airline.getIsInternationalAirline())) {
                continue;
            }
            // 国家城市 不包含出发、达到城市
            if (!countryCityList.contains(airline.getDepCity()) && !countryCityList.contains(airline.getArrCity())) {
                continue;
            }
            Set<String> depAirports = cityAirportMap.get(airline.getDepCity());
            Set<String> arrAirports = cityAirportMap.get(airline.getArrCity());
            if (depAirports == null || arrAirports == null) {
                continue;
            }
            for (String dep : depAirports) {
                for (String arr : arrAirports) {
                    segmentSet.add(dep + "-" + arr);
                }
            }
        }
        List<String> segmentList = Lists.newArrayList(segmentSet);
        Collections.sort(segmentList);
        return segmentList;
    }
}
