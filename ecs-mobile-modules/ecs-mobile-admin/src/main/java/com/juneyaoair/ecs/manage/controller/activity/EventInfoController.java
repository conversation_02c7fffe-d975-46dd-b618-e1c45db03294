package com.juneyaoair.ecs.manage.controller.activity;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.juneyaoair.ecs.manage.dto.activity.request.event.EventMemberQuery;
import com.juneyaoair.ecs.manage.dto.activity.response.event.EventInfoPO;
import com.juneyaoair.ecs.manage.dto.activity.request.event.EventInfoQueryParam;
import com.juneyaoair.ecs.manage.dto.activity.response.event.EventMemberRecord;
import com.juneyaoair.ecs.manage.dto.activity.response.event.EventPrizePO;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.external.FlightBasicService;
import com.juneyaoair.ecs.manage.service.activity.EventInfoService;
import com.juneyaoair.ecs.seat.result.SeatSidInfo;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * @Author: caolei
 * @Description: 事件信息处理
 * @Date: 2025/04/11 9:15
 * @Modified by:
 */
@RestController
public class EventInfoController {

    @Autowired
    private FlightBasicService flightBasicService;
    @Autowired
    private EventInfoService eventInfoService;

    @ApiOperation(value = "创建事件", notes = "创建事件")
    @PostMapping("/eventInfo/createEventInfo")
    public R<String> createEventInfo(@RequestBody EventInfoPO eventInfo){
        flightBasicService.createEventInfo(eventInfo);
        return R.ok(null);
    }

    @ApiOperation(value = "更新事件", notes = "更新事件")
    @PostMapping("/eventInfo/updateEventInfo")
    public R<String> updateEventInfo(@RequestBody EventInfoPO eventInfo){
        flightBasicService.updateEventInfo(eventInfo);
        return R.ok(null);
    }

    @ApiOperation(value = "新增事件奖品")
    @PostMapping("/eventInfo/createPrize")
    public R<String> createPrize(@RequestBody EventPrizePO eventPrize) {
        flightBasicService.createPrize(eventPrize);
        return R.ok(null);
    }

    @ApiOperation(value = "更新事件奖品")
    @PostMapping("/eventInfo/updatePrize")
    public R<String> updatePrize(@RequestBody EventPrizePO eventPrize) {
        flightBasicService.updatePrize(eventPrize);
        return R.ok(null);
    }

    @ApiOperation(value = "删除奖品")
    @PostMapping("/eventInfo/deletePrize")
    public R<String> deletePrize(@RequestBody @Validated EventPrizePO eventPrize) {
        flightBasicService.deletePrize(eventPrize.getEventPrizeId());
        return R.ok(null);
    }

    @ApiOperation(value = "更新事件状态", notes = "更新事件状态")
    @PostMapping("/eventInfo/updateEventStatus")
    public R<String> updateEventStatus(@RequestBody EventInfoPO eventInfo){
        flightBasicService.updateEventStatus(eventInfo.getEventInfoId(), eventInfo.getStatus());
        return R.ok(null);
    }

    @ApiOperation(value = "查询事件列表", notes = "查询事件列表")
    @PostMapping("/eventInfo/getEventInfoList")
    public R<PageResult<EventInfoPO>> getEventInfoList(@RequestBody @Validated EventInfoQueryParam eventInfoQueryParam){
        PageResult<EventInfoPO> pageResult = flightBasicService.getEventInfoList(eventInfoQueryParam);
        return R.ok(pageResult);
    }

    @ApiOperation(value = "查询事件奖品列表", notes = "查询事件奖品列表")
    @PostMapping("/eventInfo/getEventPrizeList")
    public R<List<EventPrizePO>> getEventPrizeList(@RequestBody @Validated EventInfoPO eventInfo){
        List<EventPrizePO> eventInfoList = flightBasicService.getEventPrizeList(eventInfo.getEventInfoId());
        return R.ok(eventInfoList);
    }

    @ApiOperation(value = "查询事件奖品发放清单", notes = "查询事件奖品发放清单")
    @PostMapping("/eventInfo/getEventMemberRecord")
    public R<PageResult<EventMemberRecord>> getEventMemberRecord(@RequestBody @Validated EventMemberQuery eventMemberQuery){
        PageHelper.startPage(eventMemberQuery.getPageNum(), eventMemberQuery.getPageSize());
        List<EventMemberRecord> memberRecordList = eventInfoService.getEventMemberRecord(eventMemberQuery);
        PageInfo<EventMemberRecord> pageInfo = new PageInfo<>(memberRecordList);
        PageResult<EventMemberRecord> pageResult = new PageResult<>();
        pageResult.setPageNum(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setRows(pageInfo.getList());
        return R.ok(pageResult);
    }

    @ApiOperation(value = "事件奖品发放清单下载")
    @PostMapping(value = "/eventInfo/downloadEventMemberRecord")
    public void downloadEventMemberRecord(HttpServletResponse response, @RequestBody EventMemberQuery eventMemberQuery) throws IOException {
        List<EventMemberRecord> memberRecordList = eventInfoService.getEventMemberRecord(eventMemberQuery);
        // 这里的 ContentType 要和前端请求携带的 ContentType 相对应
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 防止中文乱码
        String fileName = URLEncoder.encode("奖品发放清单", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), SeatSidInfo.class).sheet("奖品发放清单").doWrite(memberRecordList);
    }

}
