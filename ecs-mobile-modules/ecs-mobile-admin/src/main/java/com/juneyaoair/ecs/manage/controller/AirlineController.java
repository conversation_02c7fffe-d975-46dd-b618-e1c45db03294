package com.juneyaoair.ecs.manage.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.aop.CityAopMethodAnno;
import com.juneyaoair.ecs.manage.dto.airline.*;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.enums.ApplicablModelsEnum;
import com.juneyaoair.ecs.manage.service.airline.IAirlineAggrService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.ExcelUtil;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.juneyaoair.manage.b2c.entity.AirlineLabelPO;
import com.juneyaoair.manage.b2c.service.IAirlineService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import com.ruoyi.common.security.annotation.RequiresLogin;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 航线管理
 */
@Api(value = "AirLineController", tags = "航线管理")
@RequestMapping("airLine")
@RestController
@RequiredArgsConstructor
@Slf4j
public class AirlineController extends HoBaseController {

    private static String format = "2022-04-14";
    @Autowired
    private IAirlineService airLineService;

    @Autowired
    private IAirlineAggrService airLineAggrService;

    @ApiOperation(value = "分页查询航线信息", notes = "")
    @PostMapping("searchList")
    public R<PageResult<AirlineDTO>> searchPage(@RequestBody @Validated AirLineRequestDTO requestDto) {
        try {
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(requestDto));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<AirlineAPO> localPage = PageHelper.getLocalPage();
            List<AirlineDTO> airlineDTOS = airLineService.searchList(requestDto);
            R<PageResult<AirlineDTO>> pageData = getPageData(airlineDTOS, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            Context.setContext(null);
        }
        return R.fail();
    }

    @ApiOperation(value = "获取所有航线", notes = "")
    @GetMapping(value = "getAirlines")
    public R<List<AirlineDTO>> getAirlines() {
        try {
            return R.ok(airLineService.searchList(new AirLineRequestDTO()));
        } catch (Exception e) {
            log.error("查询航线信息失败{}", e.getMessage());
        }
        return R.fail();
    }

    @ApiOperation(value = "添加航线（addReverse为Y时添加返程）", notes = "")
    @PostMapping(value = "add/{addReverse}")
    @RequiresLogin
    @CityAopMethodAnno
    public R add(@RequestBody AirlineAPO airLine, @PathVariable("addReverse") String addReverse) {
        return R.ok(airLineAggrService.add(airLine, "Y".equalsIgnoreCase(addReverse)));
    }

    @PostMapping(value = "delete")
    @CityAopMethodAnno
    @RequiresLogin
    public R delete(@RequestBody AirlineAPO airLine) {
        return R.ok(airLineService.delete(airLine));
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @CityAopMethodAnno
    public R update(@RequestBody AirlineAPO airLine) {
        return R.ok(airLineService.update(airLine));
    }

    @ApiOperation(value = "用字段查询航线", notes = "")
    @PostMapping(value = "search")
    public R<List<AirlineAPO>> search(@RequestBody AirlineAPO airLine) {
        return R.ok(airLineService.search(airLine));
    }

    @ApiOperation(value = "查询所有航线标签", notes = "")
    @GetMapping(value = "selectAirLineLabel")
    public R<List<AirlineLabelPO>> selectAirLineLabel() {
        List<AirlineLabelPO> airlineLabelPOS = airLineService.selectAirLineLabel(null);
        airlineLabelPOS.forEach(
                i -> {
                    if (i == null || StrUtil.isBlank(i.fitFlightDateStr)) {
                        return;
                    }
                    i.fitFlightDateList = JSONObject.parseObject(i.fitFlightDateStr, new TypeReference<List<FitFlightDateDTO>>() {
                    });
                }
        );
        return R.ok(airlineLabelPOS);
    }

    @RequestMapping(value = "addLabel", method = RequestMethod.POST)
    @CityAopMethodAnno
    public R addLabel(@RequestBody @Validated AirLineLabelReqDTO labelPO) {
        if (labelPO.getApplicablModels().equals(ApplicablModelsEnum.APPLICABL_MODELS.getCode())) {
            labelPO.setApplicablModels(ApplicablModelsEnum.APPLICABL_MODELS.getRult());
        }
        if (airLineService.addLablel(labelPO)) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation(value = "用id搜索航线标签", notes = "")
    @RequestMapping(value = "selectLabelById", method = RequestMethod.POST)
    public R<AirlineLabelPO> selectLabelById(@RequestParam String labelId) {
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate newData = LocalDate.parse(format, formatter1);
        AirlineLabelPO label = airLineService.selectLabelById(labelId);
        if (label == null) {
            return R.ok();
        }
        if (newData.isAfter(LocalDate.parse(label.getEndTime(), formatter1))) {
            label.setApplicablModels(ApplicablModelsEnum.APPLICABL_MODELS.getRult());
        }
        if (StringUtils.isNotBlank(label.fitFlightDateStr)) {
            label.fitFlightDateList = JSONObject.parseObject(label.fitFlightDateStr, new TypeReference<List<FitFlightDateDTO>>() {
            });
        }
        return R.ok(label);
    }

    @ApiOperation(value = "更新航线标签", notes = "")
    @RequestMapping(value = "updateLabel", method = RequestMethod.POST)
    @CityAopMethodAnno
    public R updateLabel(@RequestBody @Validated AirLineLabelReqDTO labelReqDTO) {
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate newData = LocalDate.parse(format, formatter1);
        if (newData.isAfter(LocalDate.parse(labelReqDTO.getEndTime(), formatter1))) {
            labelReqDTO.setApplicablModels(ApplicablModelsEnum.APPLICABL_MODELS.getRult());
        }
        if (airLineService.updateLablel(labelReqDTO)) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation(value = "删除航线标签", notes = "")
    @RequestMapping(value = "deleteLabel", method = RequestMethod.POST)
    @CityAopMethodAnno
    public R deleteLabel(@RequestParam String labelId) {
        if (airLineService.deleteLablel(labelId)) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation(value = "导出excel", notes = "")
    @RequestMapping(value = "exportExcel", method = RequestMethod.POST)
    @ResponseBody
    public void exportExcel(@RequestBody AirLineRequestDTO requestDTO, HttpServletRequest request, HttpServletResponse response) {
        try {
            List<AirLineExportDTO> sheetList = airLineAggrService.exportList(requestDTO);
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
            fieldMap.put("depAirport", "depAirport");//出发机场
            fieldMap.put("depCity", "depCity");//出发城市
            fieldMap.put("arrAirport", "arrAirport");//落地机场
            fieldMap.put("arrCity", "arrCity");//落地城市
            fieldMap.put("carrierCompany", "carrierCompany");//航空公司
            fieldMap.put("isInternationalAirline", "isInternationalAirline");//国际航线
            fieldMap.put("addonRemark", "addonRemark");//航线类型
            fieldMap.put("isTransit", "isTransit");//经停直达
            fieldMap.put("transitCity", "transitCity");//经停城市
            fieldMap.put("transitAirport", "transitAirport");//经停机场
            fieldMap.put("isHoLine", "isHoLine");//本公司航线
            fieldMap.put("airlineFrontRemark", "airlineFrontRemark");//备注
            fieldMap.put("isBaggageDirect", "isBaggageDirect");//是否支持行李直挂
            fieldMap.put("listLabel", "listLabel");//标签
            String fileName = "航线管理导出表格";
            ExcelUtil.listToExcel(sheetList, fieldMap, fileName, response);
        } catch (Exception e) {
            log.error("导出城市信息异常{}", e.getMessage());
            throw new HoServiceException(e.getMessage());
        }
    }

    @ApiOperation(value = "导出模板", notes = "")
    @RequestMapping(value = "exportDemoExcel", method = RequestMethod.POST)
    @ResponseBody
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) {
        try {
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
            ArrayList<AirLineExportDemoDTO> list = new ArrayList<>();
            AirLineExportDemoDTO data = new AirLineExportDemoDTO();
            data.depAirport = "SHA";
            data.arrAirport = "SYX";
            data.transitAirport = "";
            data.addonRemark = "N";
            AirLineExportDemoDTO data1 = new AirLineExportDemoDTO();
            data1.depAirport = "BKK";
            data1.arrAirport = "ATH";
            data1.transitAirport = "PVG";
            data1.addonRemark = "Y";
            AirLineExportDemoDTO data2 = new AirLineExportDemoDTO();
            data2.depAirport = "PVG";
            data2.arrAirport = "LON";
            data2.transitAirport = "HEL";
            data2.addonRemark = "S";
            list.add(data);
            list.add(data1);
            list.add(data2);
            fieldMap.put("depAirport", "depAirport");//出发机场
            fieldMap.put("arrAirport", "arrAirport");//落地机场
            fieldMap.put("addonRemark", "addonRemark");//航线类型
            fieldMap.put("transitAirport", "transitAirport");//经停机场
            String fileName = "航线管理导出表格";
            ExcelUtil.listToExcel(list, fieldMap, fileName, response);
        } catch (Exception e) {
            log.error("导出城市信息异常" + e.getMessage(), e);
            throw new HoServiceException(e.getMessage());
        }
    }

    @ApiOperation(value = "excel解析后的航线导入数据库", notes = "")
    @PostMapping(value = "parseExcel")
    @RequiresLogin
    public R parseExcel(@RequestParam(value = "excelFile") MultipartFile excelFile, HttpServletRequest request) {
        airLineAggrService.parseExcel(excelFile);
        return R.ok();
    }

    @ApiOperation(value = "更新航线静态文件：airline.json & airline.js for m站 同旧的airLine/uploadAirlineStaticFileNew", notes = "")
    @GetMapping(value = "uploadStaticFile")
    public R<Boolean> uploadStaticFile() {
        return R.ok(airLineAggrService.syncJsonAndJs());
    }

    @ApiOperation(value = "更新airline.js for pc站", notes = "")
    @GetMapping(value = "syncJS")
    public R syncJS4PC() {
        if (!airLineAggrService.syncJs()) {
            return R.fail("js上传失败");
        }
        return R.ok();
    }

}
