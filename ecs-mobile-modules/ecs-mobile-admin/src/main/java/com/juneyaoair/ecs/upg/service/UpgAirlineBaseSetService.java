package com.juneyaoair.ecs.upg.service;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.upg.param.CreateAirlineBaseSetParam;
import com.juneyaoair.ecs.upg.param.QueryAirlineBaseSetParam;
import com.juneyaoair.ecs.upg.param.UpdateAirlineBaseSetParam;
import com.juneyaoair.ecs.upg.result.UpgAirlineBaseSetInfo;

/**
 * <AUTHOR>
 * @Description 升舱航线分组管理
 * @created 2023/8/16 9:23
 */
public interface UpgAirlineBaseSetService {

    /**
     * 创建航线分组
     * @param createAirlineBaseSetParam
     */
    void createAirlineBaseSet(CreateAirlineBaseSetParam createAirlineBaseSetParam);

    /**
     * 更新航线分组
     * @param updateAirlineBaseSetParam
     */
    void updateAirlineBaseSet(UpdateAirlineBaseSetParam updateAirlineBaseSetParam);

    /**
     * 删除航线分组
     * @param id
     */
    void deleteAirlineBaseSet(String id);

    /**
     * 查询航线分组
     * @param queryAirlineBaseSetParam
     * @return
     */
    PageResult<UpgAirlineBaseSetInfo> queryAirlineBaseSet(QueryAirlineBaseSetParam queryAirlineBaseSetParam);
}
