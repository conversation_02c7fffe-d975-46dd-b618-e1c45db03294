package com.juneyaoair.ecs.manage.common;

import com.ruoyi.common.core.domain.R;
import lombok.Data;

@Data
public class RDto<T> extends R<T> {
    public String traceId;

    public static <T> RDto<T> ok() {
        return restResult((T) null, SUCCESS, (String)null);
    }
    public static <T> RDto<T> ok(T data) {
        return restResult(data, SUCCESS, (String)null);
    }
    private static <T> RDto<T> restResult(T data, int code, String msg) {
        RDto<T> apiResult = new RDto();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        return apiResult;
    }
}
