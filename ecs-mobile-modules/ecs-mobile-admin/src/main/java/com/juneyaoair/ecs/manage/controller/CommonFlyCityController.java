package com.juneyaoair.ecs.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.cfc.dto.AddOrUpdateCommonFlyCityRequestDTO;
import com.juneyaoair.ecs.manage.dto.cfc.dto.CommonFlyCityDTO;
import com.juneyaoair.ecs.manage.dto.cfc.dto.CommonFlyCityRequestDTO;
import com.juneyaoair.ecs.manage.dto.cfc.dto.UpdateCommonFlyCityStatusRequestDTO;
import com.juneyaoair.ecs.manage.dto.cfc.dto.DeleteCommonFlyCityRequestDTO;
import com.juneyaoair.ecs.manage.dto.citymanage.CityInfoRespDTO;
import com.juneyaoair.ecs.manage.service.flightinfo.CommonFlyCityService;
import com.juneyaoair.manage.b2c.entity.AirlineAPO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller
@RequestMapping("commonFlyCity")
@Api(value = "CommonFlyCityController", tags = "共飞城市")
public class CommonFlyCityController extends HoBaseController {

    @Resource
    private CommonFlyCityService commonFlyCityService;

    /**
     * 查询 共飞城市规则
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "queryCommonFlyCity", method = RequestMethod.POST)
    @ResponseBody
    public R<PageResult<CommonFlyCityDTO>> queryCommonFlyCity(@RequestBody CommonFlyCityRequestDTO request) {
        startPage(TableSupport.buildPageRequest());
        Page<CommonFlyCityDTO> pageInfo = PageHelper.getLocalPage();
        List<CommonFlyCityDTO> list = commonFlyCityService.list(request);
        return getPageData(list, pageInfo);
    }

    /**
     * 添加 共飞城市规则
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "addCommonFlyCity", method = RequestMethod.POST)
    @ResponseBody
    public R addCommonFlyCity(@RequestBody AddOrUpdateCommonFlyCityRequestDTO request) {
        try {
            commonFlyCityService.add(request);
            return R.ok();
        } catch (Exception e) {
            log.error("添加共飞城市规则失败", e);
            return R.fail("添加失败：" + e.getMessage());
        }
    }

    /**
     * 更新 共飞城市规则
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "updateCommonFlyCity", method = RequestMethod.POST)
    @ResponseBody
    public R updateCommonFlyCity(@RequestBody AddOrUpdateCommonFlyCityRequestDTO request) {
        try {
            commonFlyCityService.update(request);
            return R.ok();
        } catch (Exception e) {
            log.error("更新共飞城市规则失败", e);
            return R.fail("更新失败：" + e.getMessage());
        }
    }

    /**
     * 启用、禁用 共飞城市规则
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @RequestMapping(value = "enableCommonFlyCity", method = RequestMethod.POST)
    @ResponseBody
    public R enableCommonFlyCity(@RequestBody UpdateCommonFlyCityStatusRequestDTO request) {
        try {
            if (StringUtils.isAnyBlank(request.getCfcId(), request.getStatus())) {
                return R.fail("参数不能为空");
            }
            commonFlyCityService.updateStatus(request.getCfcId(), request.getStatus());
            return R.ok();
        } catch (Exception e) {
            log.error("启用/禁用共飞城市规则失败", e);
            return R.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 删除共飞城市规则
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @RequestMapping(value = "deleteCommonFlyCity", method = RequestMethod.POST)
    @ResponseBody
    public R deleteCommonFlyCity(@RequestBody DeleteCommonFlyCityRequestDTO request) {
        try {
            if (StringUtils.isBlank(request.getCfcId())) {
                return R.fail("规则ID不能为空");
            }
            commonFlyCityService.delete(request.getCfcId());
            return R.ok();
        } catch (Exception e) {
            log.error("删除共飞城市规则失败", e);
            return R.fail("删除失败：" + e.getMessage());
        }
    }
}
