package com.juneyaoair.ecs.manage.controller.activity;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.juneyaoair.ecs.manage.controller.HoBaseController;
import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingDetailQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingRewardsRequest;
import com.juneyaoair.ecs.manage.dto.activity.request.integratingmarketingController.IntegratingMarketingTotalQueryRequest;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingDetailQueryResponse;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingRewardResponse;
import com.juneyaoair.ecs.manage.dto.activity.response.integratingmarketing.IntegratingMarketingTotalQueryResponse;
import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.service.integratingmarketing.IIntegratingMarketingService;
import com.juneyaoair.ecs.utils.Context;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.esc.manage.exception.HoServiceException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * @ClassName IntegratingMarketingController
 * @Description 全域营销
 * <AUTHOR>
 * @Date 2025/6/29 10:50
 * @Version 1.0
 */

@RequestMapping("marketing")
@RestController
@RequiredArgsConstructor
@Api(value = "IntegratingMarketingController", tags = "全域营销")
@Slf4j
public class IntegratingMarketingController extends HoBaseController {

    @Autowired
    private IIntegratingMarketingService integratingMarketingService;

    @PostMapping(value = "doTripSummaryQuery")
    @ApiOperation(value = "全员营销汇总信息查询", httpMethod = "POST")
    public R<PageResult<IntegratingMarketingTotalQueryResponse>> doTripSummaryQuery(@RequestBody @Validated IntegratingMarketingTotalQueryRequest request, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(request));

            // 调用 service 处理分页查询
            PageResult<IntegratingMarketingTotalQueryResponse> pageResult = integratingMarketingService.doTripSummaryQueryWithPage(request);

            R<PageResult<IntegratingMarketingTotalQueryResponse>> response = R.ok(pageResult);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(response));
            return response;
        } catch (Exception e) {
            log.error("入参:[{}]全员营销汇总信息查询失败:", JSON.toJSONString(request), e);
            return R.fail(e.getMessage());
        } finally {
            Context.setContext(null);
        }
    }

    @ApiOperation(value = "全员营销汇总信息下载")
    @PostMapping(value = "downloadTripSummaryList")
    public void downloadTripSummaryList(HttpServletResponse response, @RequestBody @Validated IntegratingMarketingTotalQueryRequest integratingMarketingTotalQueryRequest, BindingResult bindingResult) throws IOException {
        if (bindingResult.hasErrors()) {
            throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        List<IntegratingMarketingTotalQueryResponse> thirdCouponResponseList = integratingMarketingService.queryMarketingDataForExport(integratingMarketingTotalQueryRequest);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "全员营销汇总数据.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        EasyExcel.write(response.getOutputStream(), IntegratingMarketingTotalQueryResponse.class).sheet("全员营销汇总数据").doWrite(thirdCouponResponseList);
    }

    @PostMapping(value = "doTripDetailQuery")
    @ApiOperation(value = "全员营销明细信息查询", httpMethod = "POST")
    public R<PageResult<IntegratingMarketingDetailQueryResponse>> doTripDetailQuery(@RequestBody @Validated IntegratingMarketingDetailQueryRequest integratingMarketingDetailQueryRequest, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            initContext();
            log.info("[traceId]{}-[req]{}", Context.getContext().getId(), JsonUtil.objectToJson(integratingMarketingDetailQueryRequest));
            PageDomain pageDomain = TableSupport.buildPageRequest();
            startPage(pageDomain);
            Page<IntegratingMarketingDetailQueryResponse> localPage = PageHelper.getLocalPage();
            List<IntegratingMarketingDetailQueryResponse> thirdCouponResponseList = integratingMarketingService.doTripDetailQuery(integratingMarketingDetailQueryRequest);
            R<PageResult<IntegratingMarketingDetailQueryResponse>> pageData = getPageData(thirdCouponResponseList, localPage);
            log.info("[traceId]{}-[res]{}", Context.getContext().getId(), JsonUtil.objectToJson(pageData));
            return pageData;
        } catch (Exception exception) {
            log.error("入参:[{}]全员营销明细信息查询失败:", JSON.toJSONString(integratingMarketingDetailQueryRequest), exception);
            return R.fail(exception.getMessage());
        } finally {
            Context.setContext(null);
        }
    }

    @ApiOperation(value = "全员营销明细信息下载")
    @PostMapping(value = "downloadTripDetailList")
    public void downloadTripDetailList(HttpServletResponse response, @RequestBody @Validated IntegratingMarketingDetailQueryRequest integratingMarketingDetailQueryRequest, BindingResult bindingResult) throws IOException {
        if (bindingResult.hasErrors()) {
            throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        List<IntegratingMarketingDetailQueryResponse> marketingDetailQueryResponseList = integratingMarketingService.doTripDetailQuery(integratingMarketingDetailQueryRequest);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "全员营销明细数据.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        EasyExcel.write(response.getOutputStream(), IntegratingMarketingDetailQueryResponse.class).sheet("全员营销明细数据").doWrite(marketingDetailQueryResponseList);
    }

    @ApiOperation(value = "全员营销月度奖励数据下载")
    @PostMapping(value = "downloadTripRewardList")
    public void downloadTripRewardList(HttpServletResponse response, @RequestBody @Validated IntegratingMarketingRewardsRequest integratingMarketingRewardsRequest, BindingResult bindingResult) throws IOException {
        if (bindingResult.hasErrors()) {
            throw new HoServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        List<IntegratingMarketingRewardResponse> marketingDetailQueryResponseList = integratingMarketingService.downloadTripRewardList(integratingMarketingRewardsRequest);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "全员营销月度奖励数据.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        EasyExcel.write(response.getOutputStream(), IntegratingMarketingRewardResponse.class).sheet("全员营销月度奖励数据").doWrite(marketingDetailQueryResponseList);
    }

}
