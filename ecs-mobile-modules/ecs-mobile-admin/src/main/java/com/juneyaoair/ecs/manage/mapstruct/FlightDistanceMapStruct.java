package com.juneyaoair.ecs.manage.mapstruct;

import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedDTO;
import com.juneyaoair.ecs.manage.dto.flightdistance.FlightDistancePricedLogsDTO;
import com.juneyaoair.manage.flight.entity.TFlightDistance;
import com.juneyaoair.manage.flight.entity.TFlightDistanceLog;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(builder = @Builder(disableBuilder = true))
public interface FlightDistanceMapStruct {


    FlightDistanceMapStruct INSTANCE = org.mapstruct.factory.Mappers.getMapper(FlightDistanceMapStruct.class);


    @Mapping(source = "YPrice", target = "priceY", numberFormat = "#.00")
    @Mapping(source = "JPrice", target = "priceJ", numberFormat = "#.00")
    @Mapping(source = "depCityCode", target = "depCity")
    @Mapping(source = "arrCityCode", target = "arrCity")
    FlightDistancePricedDTO toFlightDistancePricedDTO(TFlightDistance po);


    @Mapping(source = "priceY", target = "YPrice", numberFormat = "#.00")
    @Mapping(source = "priceJ", target = "JPrice", numberFormat = "#.00")
    @Mapping(source = "depCity", target = "depCityCode")
    @Mapping(source = "arrCity", target = "arrCityCode")
    TFlightDistance toTFlightDistancePO(FlightDistancePricedDTO po);

    FlightDistancePricedLogsDTO logToFlightDistancePricedLogsDTO(TFlightDistanceLog logPo);


}
