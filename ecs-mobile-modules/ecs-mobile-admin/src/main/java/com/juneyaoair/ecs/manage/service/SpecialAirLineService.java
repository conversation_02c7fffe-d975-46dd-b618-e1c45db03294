package com.juneyaoair.ecs.manage.service;

import com.juneyaoair.ecs.manage.dto.base.PageResult;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineBase;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineBaseParam;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineInfo;
import com.juneyaoair.ecs.manage.dto.specialAirline.SpecialAirlineQueryParam;

/**
 * @Author: caolei
 * @Description: 特价航线
 * @Date: 2025/02/07 16:42
 * @Modified by:
 */
public interface SpecialAirLineService {

    /**
     * 创建特价航线
     * @param specialAirlineBaseParam
     */
    void create(SpecialAirlineBaseParam specialAirlineBaseParam);

    /**
     * 更新特价航线
     * @param specialAirlineBaseParam
     */
    void updateSpecialAirline(SpecialAirlineBaseParam specialAirlineBaseParam);

    /**
     * 删除特价航线
     * @param specialAirlineBase
     */
    void deleteSpecialAirline(SpecialAirlineBase specialAirlineBase);

    /**
     * 查询特价航线清单
     * @param specialAirlineQueryParam
     * @return
     */
    PageResult<SpecialAirlineInfo> getList(SpecialAirlineQueryParam specialAirlineQueryParam);

    /**
     * 查询指定ID特价航线
     * @param specialAirlineBase
     * @return
     */
    SpecialAirlineInfo getSpecialAirlineInfo(SpecialAirlineBase specialAirlineBase);

}
