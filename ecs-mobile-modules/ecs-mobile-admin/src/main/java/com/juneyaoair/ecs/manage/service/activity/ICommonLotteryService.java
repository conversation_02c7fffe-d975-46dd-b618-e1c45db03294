package com.juneyaoair.ecs.manage.service.activity;

import com.juneyaoair.ecs.manage.dto.activity.request.CommonLotteryPoolReq;
import com.juneyaoair.ecs.manage.dto.activity.request.CommonLotteryPrizeReq;
import com.juneyaoair.manage.b2c.entity.activity.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ICommonLotteryService
 * @Description
 * @createTime 2023年05月17日 14:51
 */
public interface ICommonLotteryService {
    /**
     * 新增奖池记录
     * @param commonLotteryPoolReq
     * @return
     */
    boolean toAddPrizePoolRecord(CommonLotteryPoolReq commonLotteryPoolReq);

    /**
     * 新增奖品记录
     * @param commonLotteryPrizeReq
     * @return
     */
    boolean toAddPrizeRecord(CommonLotteryPrizeReq commonLotteryPrizeReq);

    /**
     * 更新奖池记录
     * @param commonLotteryPoolReq
     * @return
     */
    boolean toUpdatePrizePoolRecord(CommonLotteryPoolReq commonLotteryPoolReq);

    /**
     * 获取奖池记录
     * @param commonLotteryPoolReq
     * @return
     */
    List<CommonLotteryResponse> queryLotteryPoolResult(CommonLotteryPoolReq commonLotteryPoolReq);

    /**
     * 获取奖品列表
     * @param commonLotteryPrizeReq
     * @return
     */
    CommonLotteryPrizeResp queryLotteryPrizeResult(CommonLotteryPrizeReq commonLotteryPrizeReq);

    /**
     * 更新奖品信息
     * @param commonLotteryPrizeReq
     * @return
     */
    boolean toUpdateLotteryPrizeInfo(CommonLotteryPrizeReq commonLotteryPrizeReq);

    /**
     * 删除某个奖品或整个奖池
     * @param commonLotteryReq
     * @return
     */
    boolean toDeletePoolOrPrize(CommonLotteryReq commonLotteryReq);

    /**
     * 查询中奖结果
     */
    List<CommonLotteryResultResp> queryCommonLotteryResultPage(CommonLotteryResultReq commonLotteryResultReq);

}
