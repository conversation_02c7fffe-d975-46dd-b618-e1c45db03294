package com.juneyaoair.ecs.manage.service.airport.impl;

import com.juneyaoair.ecs.manage.dto.airport.AirportLabelInfoDTO;
import com.juneyaoair.ecs.utils.SpringContextUtil;
import com.juneyaoair.manage.b2c.service.IAirportService;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;

@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
class AirportServiceImplTest {

    @Autowired
    IAirportService IAirportService;
    @Test
    void updateAirportLabel() {
        AirportLabelInfoDTO dto = new AirportLabelInfoDTO();
        dto.airportLabelName = "test";
        dto.labelIntroduce = "test";
        dto.airportLabelUrl = "test";
        boolean kdy = IAirportService.updateAirportLabel(Collections.singletonList(dto), "KDY");
        Assert.assertTrue(kdy);
    }

    @Test
    public void getActiveProfile(){
        String activeProfile = Objects.requireNonNull(SpringContextUtil.getActiveProfile()).getEnv();
        Assert.assertNotNull(activeProfile);
        System.out.println(activeProfile);
    }

}