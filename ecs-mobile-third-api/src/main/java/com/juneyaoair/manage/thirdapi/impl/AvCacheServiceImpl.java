package com.juneyaoair.manage.thirdapi.impl;

import com.juneyaoair.ecs.http.service.HttpClientService;
import com.juneyaoair.ecs.utils.JsonUtil;
import com.juneyaoair.manage.thirdapi.IAvCacheService;
import com.juneyaoair.manage.thirdapi.config.ThirdUrlSetConfig;
import com.juneyaoair.manage.thirdapi.constant.UrlConstant;
import com.juneyaoair.manage.thirdapi.dto.FareFreshNotice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/18 9:49
 */
@RefreshScope
@Service
@Slf4j
public class AvCacheServiceImpl implements IAvCacheService {
    @Autowired
    private HttpClientService httpClientService;
    @Autowired
    private ThirdUrlSetConfig thirdUrlSetConfig;
    //刷新开关，默认不刷新
    @Value("${refreshFlag:N}")
    private String refreshFlag;

    @Override
    @Async("threadPoolExecutor")
    public void refreshAvCache(FareFreshNotice fareFreshNotice) {
        String url = thirdUrlSetConfig.getOneorderServiceFareUrl()+ UrlConstant.FARE_FRESH_NOTICE;
        if("Y".equals(refreshFlag)){
            httpClientService.doPostJson(fareFreshNotice,url,null);
        }else{
            log.info("请求{},参数:{},模拟调用国际航线日历刷新接口",url, JsonUtil.objectToJson(fareFreshNotice));
        }
    }
}
